#!/bin/bash

# Script to update Lambda Layer and Function
set -e

# Configuration
LAYER_NAME="my-node-app-layer"
FUNCTION_NAME="my-node-app-api"
REGION="us-east-1"  # Change to your region

echo "🔄 Updating Lambda Layer..."

# Navigate to layer directory and install dependencies
cd lambda-layer
echo "📦 Installing/updating dependencies..."
npm install

# Create new layer package
echo "📦 Creating updated layer package..."
zip -r ../my-node-app-layer.zip .
cd ..

# Upload new layer version
echo "🚀 Publishing new layer version..."
LAYER_RESPONSE=$(aws lambda publish-layer-version \
  --layer-name $LAYER_NAME \
  --description "Updated layer - $(date)" \
  --zip-file fileb://my-node-app-layer.zip \
  --compatible-runtimes nodejs18.x nodejs20.x \
  --region $REGION)

# Extract the new layer ARN and version
LAYER_ARN=$(echo $LAYER_RESPONSE | jq -r '.LayerArn')
LAYER_VERSION=$(echo $LAYER_RESPONSE | jq -r '.Version')
FULL_LAYER_ARN="${LAYER_ARN}:${LAYER_VERSION}"

echo "✅ New layer version created: $LAYER_VERSION"
echo "📋 Layer ARN: $FULL_LAYER_ARN"

# Update Lambda function to use new layer
echo "🔄 Updating Lambda function to use new layer..."
aws lambda update-function-configuration \
  --function-name $FUNCTION_NAME \
  --layers $FULL_LAYER_ARN \
  --region $REGION

echo "✅ Lambda function updated successfully!"
echo ""
echo "📊 Summary:"
echo "  - Layer: $LAYER_NAME (version $LAYER_VERSION)"
echo "  - Function: $FUNCTION_NAME"
echo "  - Region: $REGION"
echo ""
echo "🧪 Test your function:"
echo "aws lambda invoke --function-name $FUNCTION_NAME --payload '{\"httpMethod\":\"GET\",\"path\":\"/users\"}' response.json"
