{"version": 3, "sources": ["../src/hooks.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst { logger } = require('./utils/logger');\nconst debug = logger.debugContext('hooks');\n\nconst hookTypes = {\n  beforeValidate: { params: 2 },\n  afterValidate: { params: 2 },\n  validationFailed: { params: 3 },\n  beforeCreate: { params: 2 },\n  afterCreate: { params: 2 },\n  beforeDestroy: { params: 2 },\n  afterDestroy: { params: 2 },\n  beforeRestore: { params: 2 },\n  afterRestore: { params: 2 },\n  beforeUpdate: { params: 2 },\n  afterUpdate: { params: 2 },\n  beforeSave: { params: 2, proxies: ['beforeUpdate', 'beforeCreate'] },\n  afterSave: { params: 2, proxies: ['afterUpdate', 'afterCreate'] },\n  beforeUpsert: { params: 2 },\n  afterUpsert: { params: 2 },\n  beforeBulkCreate: { params: 2 },\n  afterBulkCreate: { params: 2 },\n  beforeBulkDestroy: { params: 1 },\n  afterBulkDestroy: { params: 1 },\n  beforeBulkRestore: { params: 1 },\n  afterBulkRestore: { params: 1 },\n  beforeBulkUpdate: { params: 1 },\n  afterBulkUpdate: { params: 1 },\n  beforeFind: { params: 1 },\n  beforeFindAfterExpandIncludeAll: { params: 1 },\n  beforeFindAfterOptions: { params: 1 },\n  afterFind: { params: 2 },\n  beforeCount: { params: 1 },\n  beforeDefine: { params: 2, sync: true, noModel: true },\n  afterDefine: { params: 1, sync: true, noModel: true },\n  beforeInit: { params: 2, sync: true, noModel: true },\n  afterInit: { params: 1, sync: true, noModel: true },\n  beforeAssociate: { params: 2, sync: true },\n  afterAssociate: { params: 2, sync: true },\n  beforeConnect: { params: 1, noModel: true },\n  afterConnect: { params: 2, noModel: true },\n  beforeDisconnect: { params: 1, noModel: true },\n  afterDisconnect: { params: 1, noModel: true },\n  beforePoolAcquire: { params: 1, noModel: true },\n  afterPoolAcquire: { params: 2, noModel: true },\n  beforeSync: { params: 1 },\n  afterSync: { params: 1 },\n  beforeBulkSync: { params: 1 },\n  afterBulkSync: { params: 1 },\n  beforeQuery: { params: 2 },\n  afterQuery: { params: 2 }\n};\nexports.hooks = hookTypes;\n\n\n/**\n * get array of current hook and its proxies combined\n *\n * @param {string} hookType any hook type @see {@link hookTypes}\n *\n * @private\n */\nconst getProxiedHooks = hookType =>\n  hookTypes[hookType].proxies\n    ? hookTypes[hookType].proxies.concat(hookType)\n    : [hookType]\n;\n\nfunction getHooks(hooked, hookType) {\n  return (hooked.options.hooks || {})[hookType] || [];\n}\n\nconst Hooks = {\n  /**\n   * Process user supplied hooks definition\n   *\n   * @param {object} hooks hooks definition\n   *\n   * @private\n   * @memberof Sequelize\n   * @memberof Sequelize.Model\n   */\n  _setupHooks(hooks) {\n    this.options.hooks = {};\n    _.map(hooks || {}, (hooksArray, hookName) => {\n      if (!Array.isArray(hooksArray)) hooksArray = [hooksArray];\n      hooksArray.forEach(hookFn => this.addHook(hookName, hookFn));\n    });\n  },\n\n  async runHooks(hooks, ...hookArgs) {\n    if (!hooks) throw new Error('runHooks requires at least 1 argument');\n\n    let hookType;\n\n    if (typeof hooks === 'string') {\n      hookType = hooks;\n      hooks = getHooks(this, hookType);\n\n      if (this.sequelize) {\n        hooks = hooks.concat(getHooks(this.sequelize, hookType));\n      }\n    }\n\n    if (!Array.isArray(hooks)) {\n      hooks = [hooks];\n    }\n\n    // synchronous hooks\n    if (hookTypes[hookType] && hookTypes[hookType].sync) {\n      for (let hook of hooks) {\n        if (typeof hook === 'object') {\n          hook = hook.fn;\n        }\n\n        debug(`running hook(sync) ${hookType}`);\n        hook.apply(this, hookArgs);\n      }\n      return;\n    }\n\n    // asynchronous hooks (default)\n    for (let hook of hooks) {\n      if (typeof hook === 'object') {\n        hook = hook.fn;\n      }\n\n      debug(`running hook ${hookType}`);\n      await hook.apply(this, hookArgs);\n    }\n  },\n\n  /**\n   * Add a hook to the model\n   *\n   * @param {string}          hookType hook name @see {@link hookTypes}\n   * @param {string|Function} [name] Provide a name for the hook function. It can be used to remove the hook later or to order hooks based on some sort of priority system in the future.\n   * @param {Function}        fn The hook function\n   *\n   * @memberof Sequelize\n   * @memberof Sequelize.Model\n   */\n  addHook(hookType, name, fn) {\n    if (typeof name === 'function') {\n      fn = name;\n      name = null;\n    }\n\n    debug(`adding hook ${hookType}`);\n    // check for proxies, add them too\n    hookType = getProxiedHooks(hookType);\n\n    hookType.forEach(type => {\n      const hooks = getHooks(this, type);\n      hooks.push(name ? { name, fn } : fn);\n      this.options.hooks[type] = hooks;\n    });\n\n    return this;\n  },\n\n  /**\n   * Remove hook from the model\n   *\n   * @param {string} hookType @see {@link hookTypes}\n   * @param {string|Function} name name of hook or function reference which was attached\n   *\n   * @memberof Sequelize\n   * @memberof Sequelize.Model\n   */\n  removeHook(hookType, name) {\n    const isReference = typeof name === 'function' ? true : false;\n\n    if (!this.hasHook(hookType)) {\n      return this;\n    }\n\n    debug(`removing hook ${hookType}`);\n\n    // check for proxies, add them too\n    hookType = getProxiedHooks(hookType);\n\n    for (const type of hookType) {\n      this.options.hooks[type] = this.options.hooks[type].filter(hook => {\n        if (isReference && typeof hook === 'function') {\n          return hook !== name; // check if same method\n        }\n        if (!isReference && typeof hook === 'object') {\n          return hook.name !== name;\n        }\n        return true;\n      });\n    }\n\n    return this;\n  },\n\n  /**\n   * Check whether the mode has any hooks of this type\n   *\n   * @param {string} hookType @see {@link hookTypes}\n   *\n   * @alias hasHooks\n   *\n   * @memberof Sequelize\n   * @memberof Sequelize.Model\n   */\n  hasHook(hookType) {\n    return this.options.hooks[hookType] && !!this.options.hooks[hookType].length;\n  }\n};\nHooks.hasHooks = Hooks.hasHook;\n\n\nfunction applyTo(target, isModel = false) {\n  _.mixin(target, Hooks);\n\n  for (const hook of Object.keys(hookTypes)) {\n    if (isModel && hookTypes[hook].noModel) {\n      continue;\n    }\n    target[hook] = function(name, callback) {\n      return this.addHook(hook, name, callback);\n    };\n  }\n}\nexports.applyTo = applyTo;\n\n/**\n * A hook that is run before validation\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with instance, options\n * @name beforeValidate\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run after validation\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with instance, options\n * @name afterValidate\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run when validation fails\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with instance, options, error. Error is the\n * SequelizeValidationError. If the callback throws an error, it will replace the original validation error.\n * @name validationFailed\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before creating a single instance\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with attributes, options\n * @name beforeCreate\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run after creating a single instance\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with attributes, options\n * @name afterCreate\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before creating or updating a single instance, It proxies `beforeCreate` and `beforeUpdate`\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with attributes, options\n * @name beforeSave\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before upserting\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with attributes, options\n * @name beforeUpsert\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run after upserting\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with the result of upsert(), options\n * @name afterUpsert\n * @memberof Sequelize.Model\n */\n\n/**\n  * A hook that is run after creating or updating a single instance, It proxies `afterCreate` and `afterUpdate`\n *\n  * @param {string}   name\n  * @param {Function} fn   A callback function that is called with attributes, options\n  * @name afterSave\n  * @memberof Sequelize.Model\n  */\n\n/**\n * A hook that is run before destroying a single instance\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with instance, options\n *\n * @name beforeDestroy\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run after destroying a single instance\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with instance, options\n *\n * @name afterDestroy\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before restoring a single instance\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with instance, options\n *\n * @name beforeRestore\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run after restoring a single instance\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with instance, options\n *\n * @name afterRestore\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before updating a single instance\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with instance, options\n * @name beforeUpdate\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run after updating a single instance\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with instance, options\n * @name afterUpdate\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before creating instances in bulk\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with instances, options\n * @name beforeBulkCreate\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run after creating instances in bulk\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with instances, options\n * @name afterBulkCreate\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before destroying instances in bulk\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with options\n *\n * @name beforeBulkDestroy\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run after destroying instances in bulk\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with options\n *\n * @name afterBulkDestroy\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before restoring instances in bulk\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with options\n *\n * @name beforeBulkRestore\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run after restoring instances in bulk\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with options\n *\n * @name afterBulkRestore\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before updating instances in bulk\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with options\n * @name beforeBulkUpdate\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run after updating instances in bulk\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with options\n * @name afterBulkUpdate\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before a find (select) query\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with options\n * @name beforeFind\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before a find (select) query, after any { include: {all: ...} } options are expanded\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with options\n * @name beforeFindAfterExpandIncludeAll\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before a find (select) query, after all option parsing is complete\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with options\n * @name beforeFindAfterOptions\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run after a find (select) query\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with instance(s), options\n * @name afterFind\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before a count query\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with options\n * @name beforeCount\n * @memberof Sequelize.Model\n */\n\n/**\n * A hook that is run before a define call\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with attributes, options\n * @name beforeDefine\n * @memberof Sequelize\n */\n\n/**\n * A hook that is run after a define call\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with factory\n * @name afterDefine\n * @memberof Sequelize\n */\n\n/**\n * A hook that is run before Sequelize() call\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with config, options\n * @name beforeInit\n * @memberof Sequelize\n */\n\n/**\n * A hook that is run after Sequelize() call\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with sequelize\n * @name afterInit\n * @memberof Sequelize\n */\n\n/**\n * A hook that is run before a connection is created\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with config passed to connection\n * @name beforeConnect\n * @memberof Sequelize\n */\n\n/**\n * A hook that is run after a connection is created\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with the connection object and the config passed to connection\n * @name afterConnect\n * @memberof Sequelize\n */\n\n/**\n *  A hook that is run before a connection to the pool\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with config passed to connection\n * @name beforePoolAcquire\n * @memberof Sequelize\n */\n\n/**\n * A hook that is run after a connection to the pool\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with the connection object and the config passed to connection\n * @name afterPoolAcquire\n * @memberof Sequelize\n */\n\n/**\n * A hook that is run before a connection is disconnected\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with the connection object\n * @name beforeDisconnect\n * @memberof Sequelize\n */\n\n/**\n * A hook that is run after a connection is disconnected\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with the connection object\n * @name afterDisconnect\n * @memberof Sequelize\n */\n\n/**\n * A hook that is run before Model.sync call\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with options passed to Model.sync\n * @name beforeSync\n * @memberof Sequelize\n */\n\n/**\n * A hook that is run after Model.sync call\n *\n * @param {string}   name\n * @param {Function} fn   A callback function that is called with options passed to Model.sync\n * @name afterSync\n * @memberof Sequelize\n */\n\n/**\n  * A hook that is run before sequelize.sync call\n *\n  * @param {string}   name\n  * @param {Function} fn   A callback function that is called with options passed to sequelize.sync\n  * @name beforeBulkSync\n  * @memberof Sequelize\n  */\n\n/**\n  * A hook that is run after sequelize.sync call\n *\n  * @param {string}   name\n  * @param {Function} fn   A callback function that is called with options passed to sequelize.sync\n  * @name afterBulkSync\n  * @memberof Sequelize\n  */\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,EAAE,WAAW,QAAQ;AAC3B,MAAM,QAAQ,OAAO,aAAa;AAElC,MAAM,YAAY;AAAA,EAChB,gBAAgB,EAAE,QAAQ;AAAA,EAC1B,eAAe,EAAE,QAAQ;AAAA,EACzB,kBAAkB,EAAE,QAAQ;AAAA,EAC5B,cAAc,EAAE,QAAQ;AAAA,EACxB,aAAa,EAAE,QAAQ;AAAA,EACvB,eAAe,EAAE,QAAQ;AAAA,EACzB,cAAc,EAAE,QAAQ;AAAA,EACxB,eAAe,EAAE,QAAQ;AAAA,EACzB,cAAc,EAAE,QAAQ;AAAA,EACxB,cAAc,EAAE,QAAQ;AAAA,EACxB,aAAa,EAAE,QAAQ;AAAA,EACvB,YAAY,EAAE,QAAQ,GAAG,SAAS,CAAC,gBAAgB;AAAA,EACnD,WAAW,EAAE,QAAQ,GAAG,SAAS,CAAC,eAAe;AAAA,EACjD,cAAc,EAAE,QAAQ;AAAA,EACxB,aAAa,EAAE,QAAQ;AAAA,EACvB,kBAAkB,EAAE,QAAQ;AAAA,EAC5B,iBAAiB,EAAE,QAAQ;AAAA,EAC3B,mBAAmB,EAAE,QAAQ;AAAA,EAC7B,kBAAkB,EAAE,QAAQ;AAAA,EAC5B,mBAAmB,EAAE,QAAQ;AAAA,EAC7B,kBAAkB,EAAE,QAAQ;AAAA,EAC5B,kBAAkB,EAAE,QAAQ;AAAA,EAC5B,iBAAiB,EAAE,QAAQ;AAAA,EAC3B,YAAY,EAAE,QAAQ;AAAA,EACtB,iCAAiC,EAAE,QAAQ;AAAA,EAC3C,wBAAwB,EAAE,QAAQ;AAAA,EAClC,WAAW,EAAE,QAAQ;AAAA,EACrB,aAAa,EAAE,QAAQ;AAAA,EACvB,cAAc,EAAE,QAAQ,GAAG,MAAM,MAAM,SAAS;AAAA,EAChD,aAAa,EAAE,QAAQ,GAAG,MAAM,MAAM,SAAS;AAAA,EAC/C,YAAY,EAAE,QAAQ,GAAG,MAAM,MAAM,SAAS;AAAA,EAC9C,WAAW,EAAE,QAAQ,GAAG,MAAM,MAAM,SAAS;AAAA,EAC7C,iBAAiB,EAAE,QAAQ,GAAG,MAAM;AAAA,EACpC,gBAAgB,EAAE,QAAQ,GAAG,MAAM;AAAA,EACnC,eAAe,EAAE,QAAQ,GAAG,SAAS;AAAA,EACrC,cAAc,EAAE,QAAQ,GAAG,SAAS;AAAA,EACpC,kBAAkB,EAAE,QAAQ,GAAG,SAAS;AAAA,EACxC,iBAAiB,EAAE,QAAQ,GAAG,SAAS;AAAA,EACvC,mBAAmB,EAAE,QAAQ,GAAG,SAAS;AAAA,EACzC,kBAAkB,EAAE,QAAQ,GAAG,SAAS;AAAA,EACxC,YAAY,EAAE,QAAQ;AAAA,EACtB,WAAW,EAAE,QAAQ;AAAA,EACrB,gBAAgB,EAAE,QAAQ;AAAA,EAC1B,eAAe,EAAE,QAAQ;AAAA,EACzB,aAAa,EAAE,QAAQ;AAAA,EACvB,YAAY,EAAE,QAAQ;AAAA;AAExB,QAAQ,QAAQ;AAUhB,MAAM,kBAAkB,cACtB,UAAU,UAAU,UAChB,UAAU,UAAU,QAAQ,OAAO,YACnC,CAAC;AAGP,kBAAkB,QAAQ,UAAU;AAClC,SAAQ,QAAO,QAAQ,SAAS,IAAI,aAAa;AAAA;AAGnD,MAAM,QAAQ;AAAA,EAUZ,YAAY,OAAO;AACjB,SAAK,QAAQ,QAAQ;AACrB,MAAE,IAAI,SAAS,IAAI,CAAC,YAAY,aAAa;AAC3C,UAAI,CAAC,MAAM,QAAQ;AAAa,qBAAa,CAAC;AAC9C,iBAAW,QAAQ,YAAU,KAAK,QAAQ,UAAU;AAAA;AAAA;AAAA,QAIlD,SAAS,UAAU,UAAU;AACjC,QAAI,CAAC;AAAO,YAAM,IAAI,MAAM;AAE5B,QAAI;AAEJ,QAAI,OAAO,UAAU,UAAU;AAC7B,iBAAW;AACX,cAAQ,SAAS,MAAM;AAEvB,UAAI,KAAK,WAAW;AAClB,gBAAQ,MAAM,OAAO,SAAS,KAAK,WAAW;AAAA;AAAA;AAIlD,QAAI,CAAC,MAAM,QAAQ,QAAQ;AACzB,cAAQ,CAAC;AAAA;AAIX,QAAI,UAAU,aAAa,UAAU,UAAU,MAAM;AACnD,eAAS,QAAQ,OAAO;AACtB,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO,KAAK;AAAA;AAGd,cAAM,sBAAsB;AAC5B,aAAK,MAAM,MAAM;AAAA;AAEnB;AAAA;AAIF,aAAS,QAAQ,OAAO;AACtB,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO,KAAK;AAAA;AAGd,YAAM,gBAAgB;AACtB,YAAM,KAAK,MAAM,MAAM;AAAA;AAAA;AAAA,EAc3B,QAAQ,UAAU,MAAM,IAAI;AAC1B,QAAI,OAAO,SAAS,YAAY;AAC9B,WAAK;AACL,aAAO;AAAA;AAGT,UAAM,eAAe;AAErB,eAAW,gBAAgB;AAE3B,aAAS,QAAQ,UAAQ;AACvB,YAAM,QAAQ,SAAS,MAAM;AAC7B,YAAM,KAAK,OAAO,EAAE,MAAM,OAAO;AACjC,WAAK,QAAQ,MAAM,QAAQ;AAAA;AAG7B,WAAO;AAAA;AAAA,EAYT,WAAW,UAAU,MAAM;AACzB,UAAM,cAAc,OAAO,SAAS,aAAa,OAAO;AAExD,QAAI,CAAC,KAAK,QAAQ,WAAW;AAC3B,aAAO;AAAA;AAGT,UAAM,iBAAiB;AAGvB,eAAW,gBAAgB;AAE3B,eAAW,QAAQ,UAAU;AAC3B,WAAK,QAAQ,MAAM,QAAQ,KAAK,QAAQ,MAAM,MAAM,OAAO,UAAQ;AACjE,YAAI,eAAe,OAAO,SAAS,YAAY;AAC7C,iBAAO,SAAS;AAAA;AAElB,YAAI,CAAC,eAAe,OAAO,SAAS,UAAU;AAC5C,iBAAO,KAAK,SAAS;AAAA;AAEvB,eAAO;AAAA;AAAA;AAIX,WAAO;AAAA;AAAA,EAaT,QAAQ,UAAU;AAChB,WAAO,KAAK,QAAQ,MAAM,aAAa,CAAC,CAAC,KAAK,QAAQ,MAAM,UAAU;AAAA;AAAA;AAG1E,MAAM,WAAW,MAAM;AAGvB,iBAAiB,QAAQ,UAAU,OAAO;AACxC,IAAE,MAAM,QAAQ;AAEhB,aAAW,QAAQ,OAAO,KAAK,YAAY;AACzC,QAAI,WAAW,UAAU,MAAM,SAAS;AACtC;AAAA;AAEF,WAAO,QAAQ,SAAS,MAAM,UAAU;AACtC,aAAO,KAAK,QAAQ,MAAM,MAAM;AAAA;AAAA;AAAA;AAItC,QAAQ,UAAU;", "names": []}