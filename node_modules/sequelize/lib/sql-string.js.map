{"version": 3, "sources": ["../src/sql-string.js"], "sourcesContent": ["'use strict';\n\nconst moment = require('moment');\nconst dataTypes = require('./data-types');\nconst { logger } = require('./utils/logger');\n\nfunction arrayToList(array, timeZone, dialect, format) {\n  return array.reduce((sql, val, i) => {\n    if (i !== 0) {\n      sql += ', ';\n    }\n    if (Array.isArray(val)) {\n      sql += `(${arrayToList(val, timeZone, dialect, format)})`;\n    } else {\n      sql += escape(val, timeZone, dialect, format);\n    }\n    return sql;\n  }, '');\n}\nexports.arrayToList = arrayToList;\n\nfunction escape(val, timeZone, dialect, format) {\n  let prependN = false;\n  if (val === undefined || val === null) {\n    return 'NULL';\n  }\n  switch (typeof val) {\n    case 'boolean':\n    // SQLite doesn't have true/false support. MySQL aliases true/false to 1/0\n    // for us. Postgres actually has a boolean type with true/false literals,\n    // but sequelize doesn't use it yet.\n      if (['sqlite', 'mssql', 'oracle'].includes(dialect)) {\n        return +!!val;\n      }\n      return (!!val).toString();\n    case 'number':\n    case 'bigint':\n      return val.toString();\n    case 'string':\n    // In mssql, prepend N to all quoted vals which are originally a string (for\n    // unicode compatibility)\n      prependN = dialect === 'mssql';\n      break;\n  }\n\n  if (val instanceof Date) {\n    val = dataTypes[dialect].DATE.prototype.stringify(val, { timezone: timeZone });\n  }\n\n  if (Buffer.isBuffer(val)) {\n    if (dataTypes[dialect].BLOB) {\n      return dataTypes[dialect].BLOB.prototype.stringify(val);\n    }\n\n    return dataTypes.BLOB.prototype.stringify(val);\n  }\n\n  if (Array.isArray(val)) {\n    const partialEscape = escVal => escape(escVal, timeZone, dialect, format);\n    if (dialect === 'postgres' && !format) {\n      return dataTypes.ARRAY.prototype.stringify(val, { escape: partialEscape });\n    }\n    return arrayToList(val, timeZone, dialect, format);\n  }\n\n  if (!val.replace) {\n    throw new Error(`Invalid value ${logger.inspect(val)}`);\n  }\n\n  if (['postgres', 'sqlite', 'mssql', 'snowflake', 'db2'].includes(dialect)) {\n    // http://www.postgresql.org/docs/8.2/static/sql-syntax-lexical.html#SQL-SYNTAX-STRINGS\n    // http://stackoverflow.com/q/603572/130598\n    val = val.replace(/'/g, \"''\");\n\n    if (dialect === 'postgres') {\n      // null character is not allowed in Postgres\n      val = val.replace(/\\0/g, '\\\\0');\n    }\n  } else if (dialect === 'oracle' && typeof val === 'string') {\n    if (val.startsWith('TO_TIMESTAMP_TZ') || val.startsWith('TO_DATE')) {\n      // Split the string using parentheses to isolate the function name, parameters, and potential extra parts\n      const splitVal = val.split(/\\(|\\)/);\n    \n      // Validate that the split result has exactly three parts (function name, parameters, and an empty string)\n      // and that there are no additional SQL commands after the function call (indicated by the last empty string).\n      if (splitVal.length !== 3 || splitVal[2] !== '') {\n        throw new Error('Invalid SQL function call.'); // Error if function call has unexpected format\n      }\n    \n      // Extract the function name (either 'TO_TIMESTAMP_TZ' or 'TO_DATE') and the contents inside the parentheses\n      const functionName = splitVal[0].trim(); // Function name should be 'TO_TIMESTAMP_TZ' or 'TO_DATE'\n      const insideParens = splitVal[1].trim(); // This contains the parameters (date value and format string)\n    \n      if (functionName !== 'TO_TIMESTAMP_TZ' && functionName !== 'TO_DATE') {\n        throw new Error('Invalid SQL function call. Expected TO_TIMESTAMP_TZ or TO_DATE.');\n      }\n    \n      // Split the parameters inside the parentheses by commas (should contain exactly two: date and format)\n      const params = insideParens.split(',');\n    \n      // Validate that the parameters contain exactly two parts (date value and format string)\n      if (params.length !== 2) {\n        throw new Error('Unexpected input received.\\nSequelize supports TO_TIMESTAMP_TZ or TO_DATE exclusively with a combination of value and format.');\n      }\n    \n      // Extract the date value (first parameter) and remove single quotes around it\n      const dateValue = params[0].trim().replace(/'/g, '');\n      const formatValue = params[1].trim();\n    \n      if (functionName === 'TO_TIMESTAMP_TZ') {\n        const expectedFormat = \"'YYYY-MM-DD HH24:MI:SS.FFTZH:TZM'\";\n        // Validate that the formatValue is equal to expectedFormat since that is the only format used within sequelize\n        if (formatValue !== expectedFormat) {\n          throw new Error(`Invalid format string for TO_TIMESTAMP_TZ. Expected format: ${expectedFormat}`);\n        }\n      \n        // Validate the date value using Moment.js with the expected format\n        const formattedDate = moment(dateValue).format('YYYY-MM-DD HH:mm:ss.SSS Z');\n      \n        // If the formatted date doesn't match the input date value, throw an error\n        if (formattedDate !== dateValue) {\n          throw new Error(\"Invalid date value for TO_TIMESTAMP_TZ. Expected format: 'YYYY-MM-DD HH:mm:ss.SSS Z'\");\n        }\n      } else if (functionName === 'TO_DATE') {\n        const expectedFormat = \"'YYYY/MM/DD'\";\n        // Validate that the formatValue is equal to expectedFormat since that is the only format used within sequelize\n        if (formatValue !== expectedFormat) {\n          throw new Error(`Invalid format string for TO_DATE. Expected format: ${expectedFormat}`);\n        }\n      \n        // Validate the date value using Moment.js with the expected format\n        const formattedDate = moment(dateValue).format('YYYY-MM-DD');\n      \n        // If the formatted date doesn't match the input date value, throw an error\n        if (formattedDate !== dateValue) {\n          throw new Error(\"Invalid date value for TO_DATE. Expected format: 'YYYY-MM-DD'\");\n        }\n      }\n\n      return val;\n    }\n    \n    val = val.replace(/'/g, \"''\");\n  } else {\n\n    // eslint-disable-next-line no-control-regex\n    val = val.replace(/[\\0\\n\\r\\b\\t\\\\'\"\\x1a]/g, s => {\n      switch (s) {\n        case '\\0': return '\\\\0';\n        case '\\n': return '\\\\n';\n        case '\\r': return '\\\\r';\n        case '\\b': return '\\\\b';\n        case '\\t': return '\\\\t';\n        case '\\x1a': return '\\\\Z';\n        default: return `\\\\${s}`;\n      }\n    });\n  }\n  return `${(prependN ? \"N'\" : \"'\") + val}'`;\n}\nexports.escape = escape;\n\nfunction format(sql, values, timeZone, dialect) {\n  values = [].concat(values);\n\n  if (typeof sql !== 'string') {\n    throw new Error(`Invalid SQL string provided: ${sql}`);\n  }\n\n  return sql.replace(/\\?/g, match => {\n    if (!values.length) {\n      return match;\n    }\n\n    return escape(values.shift(), timeZone, dialect, true);\n  });\n}\nexports.format = format;\n\nfunction formatNamedParameters(sql, values, timeZone, dialect) {\n  return sql.replace(/:+(?!\\d)(\\w+)/g, (value, key) => {\n    if ('postgres' === dialect && '::' === value.slice(0, 2)) {\n      return value;\n    }\n\n    if (values[key] !== undefined) {\n      return escape(values[key], timeZone, dialect, true);\n    }\n    throw new Error(`Named parameter \"${value}\" has no value in the given object.`);\n  });\n}\nexports.formatNamedParameters = formatNamedParameters;\n"], "mappings": ";AAEA,MAAM,SAAS,QAAQ;AACvB,MAAM,YAAY,QAAQ;AAC1B,MAAM,EAAE,WAAW,QAAQ;AAE3B,qBAAqB,OAAO,UAAU,SAAS,SAAQ;AACrD,SAAO,MAAM,OAAO,CAAC,KAAK,KAAK,MAAM;AACnC,QAAI,MAAM,GAAG;AACX,aAAO;AAAA;AAET,QAAI,MAAM,QAAQ,MAAM;AACtB,aAAO,IAAI,YAAY,KAAK,UAAU,SAAS;AAAA,WAC1C;AACL,aAAO,OAAO,KAAK,UAAU,SAAS;AAAA;AAExC,WAAO;AAAA,KACN;AAAA;AAEL,QAAQ,cAAc;AAEtB,gBAAgB,KAAK,UAAU,SAAS,SAAQ;AAC9C,MAAI,WAAW;AACf,MAAI,QAAQ,UAAa,QAAQ,MAAM;AACrC,WAAO;AAAA;AAET,UAAQ,OAAO;AAAA,SACR;AAIH,UAAI,CAAC,UAAU,SAAS,UAAU,SAAS,UAAU;AACnD,eAAO,CAAC,CAAC,CAAC;AAAA;AAEZ,aAAQ,EAAC,CAAC,KAAK;AAAA,SACZ;AAAA,SACA;AACH,aAAO,IAAI;AAAA,SACR;AAGH,iBAAW,YAAY;AACvB;AAAA;AAGJ,MAAI,eAAe,MAAM;AACvB,UAAM,UAAU,SAAS,KAAK,UAAU,UAAU,KAAK,EAAE,UAAU;AAAA;AAGrE,MAAI,OAAO,SAAS,MAAM;AACxB,QAAI,UAAU,SAAS,MAAM;AAC3B,aAAO,UAAU,SAAS,KAAK,UAAU,UAAU;AAAA;AAGrD,WAAO,UAAU,KAAK,UAAU,UAAU;AAAA;AAG5C,MAAI,MAAM,QAAQ,MAAM;AACtB,UAAM,gBAAgB,YAAU,OAAO,QAAQ,UAAU,SAAS;AAClE,QAAI,YAAY,cAAc,CAAC,SAAQ;AACrC,aAAO,UAAU,MAAM,UAAU,UAAU,KAAK,EAAE,QAAQ;AAAA;AAE5D,WAAO,YAAY,KAAK,UAAU,SAAS;AAAA;AAG7C,MAAI,CAAC,IAAI,SAAS;AAChB,UAAM,IAAI,MAAM,iBAAiB,OAAO,QAAQ;AAAA;AAGlD,MAAI,CAAC,YAAY,UAAU,SAAS,aAAa,OAAO,SAAS,UAAU;AAGzE,UAAM,IAAI,QAAQ,MAAM;AAExB,QAAI,YAAY,YAAY;AAE1B,YAAM,IAAI,QAAQ,OAAO;AAAA;AAAA,aAElB,YAAY,YAAY,OAAO,QAAQ,UAAU;AAC1D,QAAI,IAAI,WAAW,sBAAsB,IAAI,WAAW,YAAY;AAElE,YAAM,WAAW,IAAI,MAAM;AAI3B,UAAI,SAAS,WAAW,KAAK,SAAS,OAAO,IAAI;AAC/C,cAAM,IAAI,MAAM;AAAA;AAIlB,YAAM,eAAe,SAAS,GAAG;AACjC,YAAM,eAAe,SAAS,GAAG;AAEjC,UAAI,iBAAiB,qBAAqB,iBAAiB,WAAW;AACpE,cAAM,IAAI,MAAM;AAAA;AAIlB,YAAM,SAAS,aAAa,MAAM;AAGlC,UAAI,OAAO,WAAW,GAAG;AACvB,cAAM,IAAI,MAAM;AAAA;AAIlB,YAAM,YAAY,OAAO,GAAG,OAAO,QAAQ,MAAM;AACjD,YAAM,cAAc,OAAO,GAAG;AAE9B,UAAI,iBAAiB,mBAAmB;AACtC,cAAM,iBAAiB;AAEvB,YAAI,gBAAgB,gBAAgB;AAClC,gBAAM,IAAI,MAAM,+DAA+D;AAAA;AAIjF,cAAM,gBAAgB,OAAO,WAAW,OAAO;AAG/C,YAAI,kBAAkB,WAAW;AAC/B,gBAAM,IAAI,MAAM;AAAA;AAAA,iBAET,iBAAiB,WAAW;AACrC,cAAM,iBAAiB;AAEvB,YAAI,gBAAgB,gBAAgB;AAClC,gBAAM,IAAI,MAAM,uDAAuD;AAAA;AAIzE,cAAM,gBAAgB,OAAO,WAAW,OAAO;AAG/C,YAAI,kBAAkB,WAAW;AAC/B,gBAAM,IAAI,MAAM;AAAA;AAAA;AAIpB,aAAO;AAAA;AAGT,UAAM,IAAI,QAAQ,MAAM;AAAA,SACnB;AAGL,UAAM,IAAI,QAAQ,yBAAyB,OAAK;AAC9C,cAAQ;AAAA,aACD;AAAM,iBAAO;AAAA,aACb;AAAM,iBAAO;AAAA,aACb;AAAM,iBAAO;AAAA,aACb;AAAM,iBAAO;AAAA,aACb;AAAM,iBAAO;AAAA,aACb;AAAQ,iBAAO;AAAA;AACX,iBAAO,KAAK;AAAA;AAAA;AAAA;AAI3B,SAAO,GAAI,YAAW,OAAO,OAAO;AAAA;AAEtC,QAAQ,SAAS;AAEjB,gBAAgB,KAAK,QAAQ,UAAU,SAAS;AAC9C,WAAS,GAAG,OAAO;AAEnB,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,MAAM,gCAAgC;AAAA;AAGlD,SAAO,IAAI,QAAQ,OAAO,WAAS;AACjC,QAAI,CAAC,OAAO,QAAQ;AAClB,aAAO;AAAA;AAGT,WAAO,OAAO,OAAO,SAAS,UAAU,SAAS;AAAA;AAAA;AAGrD,QAAQ,SAAS;AAEjB,+BAA+B,KAAK,QAAQ,UAAU,SAAS;AAC7D,SAAO,IAAI,QAAQ,kBAAkB,CAAC,OAAO,QAAQ;AACnD,QAAI,AAAe,YAAf,cAA0B,AAAS,MAAM,MAAM,GAAG,OAAxB,MAA4B;AACxD,aAAO;AAAA;AAGT,QAAI,OAAO,SAAS,QAAW;AAC7B,aAAO,OAAO,OAAO,MAAM,UAAU,SAAS;AAAA;AAEhD,UAAM,IAAI,MAAM,oBAAoB;AAAA;AAAA;AAGxC,QAAQ,wBAAwB;", "names": []}