{"version": 3, "sources": ["../src/deferrable.js"], "sourcesContent": ["'use strict';\n\nconst { classToInvokable } = require('./utils');\n\nclass ABSTRACT {\n  static toString(...args) {\n    return new this().toString(...args);\n  }\n\n  toString(...args) {\n    return this.toSql(...args);\n  }\n\n  toSql() {\n    throw new Error('toSql implementation missing');\n  }\n}\n\nclass INITIALLY_DEFERRED extends ABSTRACT {\n  toSql() {\n    return 'DEFERRABLE INITIALLY DEFERRED';\n  }\n}\n\nclass INITIALLY_IMMEDIATE extends ABSTRACT {\n  toSql() {\n    return 'DEFERRABLE INITIALLY IMMEDIATE';\n  }\n}\n\nclass NOT extends ABSTRACT {\n  toSql() {\n    return 'NOT DEFERRABLE';\n  }\n}\n\nclass SET_DEFERRED extends ABSTRACT {\n  constructor(constraints) {\n    super();\n    this.constraints = constraints;\n  }\n\n  toSql(queryGenerator) {\n    return queryGenerator.setDeferredQuery(this.constraints);\n  }\n}\n\nclass SET_IMMEDIATE extends ABSTRACT {\n  constructor(constraints) {\n    super();\n    this.constraints = constraints;\n  }\n\n  toSql(queryGenerator) {\n    return queryGenerator.setImmediateQuery(this.constraints);\n  }\n}\n\n/**\n * A collection of properties related to deferrable constraints. It can be used to\n * make foreign key constraints deferrable and to set the constraints within a\n * transaction. This is only supported in PostgreSQL.\n *\n * The foreign keys can be configured like this. It will create a foreign key\n * that will check the constraints immediately when the data was inserted.\n *\n * ```js\n * sequelize.define('Model', {\n *   foreign_id: {\n *     type: Sequelize.INTEGER,\n *     references: {\n *       model: OtherModel,\n *       key: 'id',\n *       deferrable: Sequelize.Deferrable.INITIALLY_IMMEDIATE\n *     }\n *   }\n * });\n * ```\n *\n * The constraints can be configured in a transaction like this. It will\n * trigger a query once the transaction has been started and set the constraints\n * to be checked at the very end of the transaction.\n *\n * ```js\n * sequelize.transaction({\n *   deferrable: Sequelize.Deferrable.SET_DEFERRED\n * });\n * ```\n *\n * @property INITIALLY_DEFERRED    Use when declaring a constraint. Allow and enable by default this constraint's checks to be deferred at the end of transactions.\n * @property INITIALLY_IMMEDIATE   Use when declaring a constraint. Allow the constraint's checks to be deferred at the end of transactions.\n * @property NOT                   Use when declaring a constraint. Set the constraint to not deferred. This is the default in PostgreSQL and makes it impossible to dynamically defer the constraints within a transaction.\n * @property SET_DEFERRED          Use when declaring a transaction. Defer the deferrable checks involved in this transaction at commit.\n * @property SET_IMMEDIATE         Use when declaring a transaction. Execute the deferrable checks involved in this transaction immediately.\n */\n\nconst Deferrable = {\n  INITIALLY_DEFERRED: classToInvokable(INITIALLY_DEFERRED),\n  INITIALLY_IMMEDIATE: classToInvokable(INITIALLY_IMMEDIATE),\n  NOT: classToInvokable(NOT),\n  SET_DEFERRED: classToInvokable(SET_DEFERRED),\n  SET_IMMEDIATE: classToInvokable(SET_IMMEDIATE)\n};\n\nmodule.exports = Deferrable;\n"], "mappings": ";AAEA,MAAM,EAAE,qBAAqB,QAAQ;AAErC,eAAe;AAAA,SACN,YAAY,MAAM;AACvB,WAAO,IAAI,OAAO,SAAS,GAAG;AAAA;AAAA,EAGhC,YAAY,MAAM;AAChB,WAAO,KAAK,MAAM,GAAG;AAAA;AAAA,EAGvB,QAAQ;AACN,UAAM,IAAI,MAAM;AAAA;AAAA;AAIpB,iCAAiC,SAAS;AAAA,EACxC,QAAQ;AACN,WAAO;AAAA;AAAA;AAIX,kCAAkC,SAAS;AAAA,EACzC,QAAQ;AACN,WAAO;AAAA;AAAA;AAIX,kBAAkB,SAAS;AAAA,EACzB,QAAQ;AACN,WAAO;AAAA;AAAA;AAIX,2BAA2B,SAAS;AAAA,EAClC,YAAY,aAAa;AACvB;AACA,SAAK,cAAc;AAAA;AAAA,EAGrB,MAAM,gBAAgB;AACpB,WAAO,eAAe,iBAAiB,KAAK;AAAA;AAAA;AAIhD,4BAA4B,SAAS;AAAA,EACnC,YAAY,aAAa;AACvB;AACA,SAAK,cAAc;AAAA;AAAA,EAGrB,MAAM,gBAAgB;AACpB,WAAO,eAAe,kBAAkB,KAAK;AAAA;AAAA;AA0CjD,MAAM,aAAa;AAAA,EACjB,oBAAoB,iBAAiB;AAAA,EACrC,qBAAqB,iBAAiB;AAAA,EACtC,KAAK,iBAAiB;AAAA,EACtB,cAAc,iBAAiB;AAAA,EAC/B,eAAe,iBAAiB;AAAA;AAGlC,OAAO,UAAU;", "names": []}