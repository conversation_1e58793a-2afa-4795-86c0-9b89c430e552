{"version": 3, "sources": ["../../src/utils/validator-extras.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst validator = _.cloneDeep(require('validator'));\nconst moment = require('moment');\n\nconst extensions = {\n  extend(name, fn) {\n    this[name] = fn;\n\n    return this;\n  },\n  notEmpty(str) {\n    return !str.match(/^[\\s\\t\\r\\n]*$/);\n  },\n  len(str, min, max) {\n    return this.isLength(str, min, max);\n  },\n  isUrl(str) {\n    return this.isURL(str);\n  },\n  isIPv6(str) {\n    return this.isIP(str, 6);\n  },\n  isIPv4(str) {\n    return this.isIP(str, 4);\n  },\n  notIn(str, values) {\n    return !this.isIn(str, values);\n  },\n  regex(str, pattern, modifiers) {\n    str += '';\n    if (Object.prototype.toString.call(pattern).slice(8, -1) !== 'RegExp') {\n      pattern = new RegExp(pattern, modifiers);\n    }\n    return str.match(pattern);\n  },\n  notRegex(str, pattern, modifiers) {\n    return !this.regex(str, pattern, modifiers);\n  },\n  isDecimal(str) {\n    return str !== '' && !!str.match(/^(?:-?(?:[0-9]+))?(?:\\.[0-9]*)?(?:[eE][+-]?(?:[0-9]+))?$/);\n  },\n  min(str, val) {\n    const number = parseFloat(str);\n    return isNaN(number) || number >= val;\n  },\n  max(str, val) {\n    const number = parseFloat(str);\n    return isNaN(number) || number <= val;\n  },\n  not(str, pattern, modifiers) {\n    return this.notRegex(str, pattern, modifiers);\n  },\n  contains(str, elem) {\n    return !!elem && str.includes(elem);\n  },\n  notContains(str, elem) {\n    return !this.contains(str, elem);\n  },\n  is(str, pattern, modifiers) {\n    return this.regex(str, pattern, modifiers);\n  }\n};\nexports.extensions = extensions;\n\n// instance based validators\nvalidator.isImmutable = function(value, validatorArgs, field, modelInstance) {\n  return modelInstance.isNewRecord || modelInstance.dataValues[field] === modelInstance._previousDataValues[field];\n};\n\n// extra validators\nvalidator.notNull = function(val) {\n  return val !== null && val !== undefined;\n};\n\n// https://github.com/chriso/validator.js/blob/6.2.0/validator.js\n_.forEach(extensions, (extend, key) => {\n  validator[key] = extend;\n});\n\n// map isNull to isEmpty\n// https://github.com/chriso/validator.js/commit/e33d38a26ee2f9666b319adb67c7fc0d3dea7125\nvalidator.isNull = validator.isEmpty;\n\n// isDate removed in 7.0.0\n// https://github.com/chriso/validator.js/commit/095509fc707a4dc0e99f85131df1176ad6389fc9\nvalidator.isDate = function(dateString) {\n  // avoid http://momentjs.com/guides/#/warnings/js-date/\n  // by doing a preliminary check on `dateString`\n  const parsed = Date.parse(dateString);\n  if (isNaN(parsed)) {\n    // fail if we can't parse it\n    return false;\n  }\n  // otherwise convert to ISO 8601 as moment prefers\n  // http://momentjs.com/docs/#/parsing/string/\n  const date = new Date(parsed);\n  return moment(date.toISOString()).isValid();\n};\n\nexports.validator = validator;\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,YAAY,EAAE,UAAU,QAAQ;AACtC,MAAM,SAAS,QAAQ;AAEvB,MAAM,aAAa;AAAA,EACjB,OAAO,MAAM,IAAI;AACf,SAAK,QAAQ;AAEb,WAAO;AAAA;AAAA,EAET,SAAS,KAAK;AACZ,WAAO,CAAC,IAAI,MAAM;AAAA;AAAA,EAEpB,IAAI,KAAK,KAAK,KAAK;AACjB,WAAO,KAAK,SAAS,KAAK,KAAK;AAAA;AAAA,EAEjC,MAAM,KAAK;AACT,WAAO,KAAK,MAAM;AAAA;AAAA,EAEpB,OAAO,KAAK;AACV,WAAO,KAAK,KAAK,KAAK;AAAA;AAAA,EAExB,OAAO,KAAK;AACV,WAAO,KAAK,KAAK,KAAK;AAAA;AAAA,EAExB,MAAM,KAAK,QAAQ;AACjB,WAAO,CAAC,KAAK,KAAK,KAAK;AAAA;AAAA,EAEzB,MAAM,KAAK,SAAS,WAAW;AAC7B,WAAO;AACP,QAAI,OAAO,UAAU,SAAS,KAAK,SAAS,MAAM,GAAG,QAAQ,UAAU;AACrE,gBAAU,IAAI,OAAO,SAAS;AAAA;AAEhC,WAAO,IAAI,MAAM;AAAA;AAAA,EAEnB,SAAS,KAAK,SAAS,WAAW;AAChC,WAAO,CAAC,KAAK,MAAM,KAAK,SAAS;AAAA;AAAA,EAEnC,UAAU,KAAK;AACb,WAAO,QAAQ,MAAM,CAAC,CAAC,IAAI,MAAM;AAAA;AAAA,EAEnC,IAAI,KAAK,KAAK;AACZ,UAAM,SAAS,WAAW;AAC1B,WAAO,MAAM,WAAW,UAAU;AAAA;AAAA,EAEpC,IAAI,KAAK,KAAK;AACZ,UAAM,SAAS,WAAW;AAC1B,WAAO,MAAM,WAAW,UAAU;AAAA;AAAA,EAEpC,IAAI,KAAK,SAAS,WAAW;AAC3B,WAAO,KAAK,SAAS,KAAK,SAAS;AAAA;AAAA,EAErC,SAAS,KAAK,MAAM;AAClB,WAAO,CAAC,CAAC,QAAQ,IAAI,SAAS;AAAA;AAAA,EAEhC,YAAY,KAAK,MAAM;AACrB,WAAO,CAAC,KAAK,SAAS,KAAK;AAAA;AAAA,EAE7B,GAAG,KAAK,SAAS,WAAW;AAC1B,WAAO,KAAK,MAAM,KAAK,SAAS;AAAA;AAAA;AAGpC,QAAQ,aAAa;AAGrB,UAAU,cAAc,SAAS,OAAO,eAAe,OAAO,eAAe;AAC3E,SAAO,cAAc,eAAe,cAAc,WAAW,WAAW,cAAc,oBAAoB;AAAA;AAI5G,UAAU,UAAU,SAAS,KAAK;AAChC,SAAO,QAAQ,QAAQ,QAAQ;AAAA;AAIjC,EAAE,QAAQ,YAAY,CAAC,QAAQ,QAAQ;AACrC,YAAU,OAAO;AAAA;AAKnB,UAAU,SAAS,UAAU;AAI7B,UAAU,SAAS,SAAS,YAAY;AAGtC,QAAM,SAAS,KAAK,MAAM;AAC1B,MAAI,MAAM,SAAS;AAEjB,WAAO;AAAA;AAIT,QAAM,OAAO,IAAI,KAAK;AACtB,SAAO,OAAO,KAAK,eAAe;AAAA;AAGpC,QAAQ,YAAY;", "names": []}