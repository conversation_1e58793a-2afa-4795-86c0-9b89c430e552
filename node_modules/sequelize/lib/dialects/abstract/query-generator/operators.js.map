{"version": 3, "sources": ["../../../../src/dialects/abstract/query-generator/operators.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst Op = require('../../../operators');\nconst Utils = require('../../../utils');\n\nconst OperatorHelpers = {\n  OperatorMap: {\n    [Op.eq]: '=',\n    [Op.ne]: '!=',\n    [Op.gte]: '>=',\n    [Op.gt]: '>',\n    [Op.lte]: '<=',\n    [Op.lt]: '<',\n    [Op.not]: 'IS NOT',\n    [Op.is]: 'IS',\n    [Op.in]: 'IN',\n    [Op.notIn]: 'NOT IN',\n    [Op.like]: 'LIKE',\n    [Op.notLike]: 'NOT LIKE',\n    [Op.iLike]: 'ILIKE',\n    [Op.notILike]: 'NOT ILIKE',\n    [Op.startsWith]: 'LIKE',\n    [Op.endsWith]: 'LIKE',\n    [Op.substring]: 'LIKE',\n    [Op.regexp]: '~',\n    [Op.notRegexp]: '!~',\n    [Op.iRegexp]: '~*',\n    [Op.notIRegexp]: '!~*',\n    [Op.between]: 'BETWEEN',\n    [Op.notBetween]: 'NOT BETWEEN',\n    [Op.overlap]: '&&',\n    [Op.contains]: '@>',\n    [Op.contained]: '<@',\n    [Op.adjacent]: '-|-',\n    [Op.strictLeft]: '<<',\n    [Op.strictRight]: '>>',\n    [Op.noExtendRight]: '&<',\n    [Op.noExtendLeft]: '&>',\n    [Op.any]: 'ANY',\n    [Op.all]: 'ALL',\n    [Op.and]: ' AND ',\n    [Op.or]: ' OR ',\n    [Op.col]: 'COL',\n    [Op.placeholder]: '$$PLACEHOLDER$$',\n    [Op.match]: '@@'\n  },\n\n  OperatorsAliasMap: {},\n\n  setOperatorsAliases(aliases) {\n    if (!aliases || _.isEmpty(aliases)) {\n      this.OperatorsAliasMap = false;\n    } else {\n      this.OperatorsAliasMap = { ...aliases };\n    }\n  },\n\n  _replaceAliases(orig) {\n    const obj = {};\n    if (!this.OperatorsAliasMap) {\n      return orig;\n    }\n\n    Utils.getOperators(orig).forEach(op => {\n      const item = orig[op];\n      if (_.isPlainObject(item)) {\n        obj[op] = this._replaceAliases(item);\n      } else {\n        obj[op] = item;\n      }\n    });\n\n    _.forOwn(orig, (item, prop) => {\n      prop = this.OperatorsAliasMap[prop] || prop;\n      if (_.isPlainObject(item)) {\n        item = this._replaceAliases(item);\n      }\n      obj[prop] = item;\n    });\n    return obj;\n  }\n};\n\nmodule.exports = OperatorHelpers;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,KAAK,QAAQ;AACnB,MAAM,QAAQ,QAAQ;AAEtB,MAAM,kBAAkB;AAAA,EACtB,aAAa;AAAA,KACV,GAAG,KAAK;AAAA,KACR,GAAG,KAAK;AAAA,KACR,GAAG,MAAM;AAAA,KACT,GAAG,KAAK;AAAA,KACR,GAAG,MAAM;AAAA,KACT,GAAG,KAAK;AAAA,KACR,GAAG,MAAM;AAAA,KACT,GAAG,KAAK;AAAA,KACR,GAAG,KAAK;AAAA,KACR,GAAG,QAAQ;AAAA,KACX,GAAG,OAAO;AAAA,KACV,GAAG,UAAU;AAAA,KACb,GAAG,QAAQ;AAAA,KACX,GAAG,WAAW;AAAA,KACd,GAAG,aAAa;AAAA,KAChB,GAAG,WAAW;AAAA,KACd,GAAG,YAAY;AAAA,KACf,GAAG,SAAS;AAAA,KACZ,GAAG,YAAY;AAAA,KACf,GAAG,UAAU;AAAA,KACb,GAAG,aAAa;AAAA,KAChB,GAAG,UAAU;AAAA,KACb,GAAG,aAAa;AAAA,KAChB,GAAG,UAAU;AAAA,KACb,GAAG,WAAW;AAAA,KACd,GAAG,YAAY;AAAA,KACf,GAAG,WAAW;AAAA,KACd,GAAG,aAAa;AAAA,KAChB,GAAG,cAAc;AAAA,KACjB,GAAG,gBAAgB;AAAA,KACnB,GAAG,eAAe;AAAA,KAClB,GAAG,MAAM;AAAA,KACT,GAAG,MAAM;AAAA,KACT,GAAG,MAAM;AAAA,KACT,GAAG,KAAK;AAAA,KACR,GAAG,MAAM;AAAA,KACT,GAAG,cAAc;AAAA,KACjB,GAAG,QAAQ;AAAA;AAAA,EAGd,mBAAmB;AAAA,EAEnB,oBAAoB,SAAS;AAC3B,QAAI,CAAC,WAAW,EAAE,QAAQ,UAAU;AAClC,WAAK,oBAAoB;AAAA,WACpB;AACL,WAAK,oBAAoB,mBAAK;AAAA;AAAA;AAAA,EAIlC,gBAAgB,MAAM;AACpB,UAAM,MAAM;AACZ,QAAI,CAAC,KAAK,mBAAmB;AAC3B,aAAO;AAAA;AAGT,UAAM,aAAa,MAAM,QAAQ,QAAM;AACrC,YAAM,OAAO,KAAK;AAClB,UAAI,EAAE,cAAc,OAAO;AACzB,YAAI,MAAM,KAAK,gBAAgB;AAAA,aAC1B;AACL,YAAI,MAAM;AAAA;AAAA;AAId,MAAE,OAAO,MAAM,CAAC,MAAM,SAAS;AAC7B,aAAO,KAAK,kBAAkB,SAAS;AACvC,UAAI,EAAE,cAAc,OAAO;AACzB,eAAO,KAAK,gBAAgB;AAAA;AAE9B,UAAI,QAAQ;AAAA;AAEd,WAAO;AAAA;AAAA;AAIX,OAAO,UAAU;", "names": []}