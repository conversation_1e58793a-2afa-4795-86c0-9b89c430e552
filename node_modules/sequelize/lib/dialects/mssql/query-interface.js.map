{"version": 3, "sources": ["../../../src/dialects/mssql/query-interface.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\n\nconst Utils = require('../../utils');\nconst QueryTypes = require('../../query-types');\nconst Op = require('../../operators');\nconst { QueryInterface } = require('../abstract/query-interface');\n\n/**\n * The interface that <PERSON><PERSON><PERSON> uses to talk with MSSQL database\n */\nclass MSSqlQueryInterface extends QueryInterface {\n  /**\n  * A wrapper that fixes MSSQL's inability to cleanly remove columns from existing tables if they have a default constraint.\n  *\n  * @override\n  */\n  async removeColumn(tableName, attributeName, options) {\n    options = { raw: true, ...options || {} };\n\n    const findConstraintSql = this.queryGenerator.getDefaultConstraintQuery(tableName, attributeName);\n    const [results0] = await this.sequelize.query(findConstraintSql, options);\n    if (results0.length) {\n      // No default constraint found -- we can cleanly remove the column\n      const dropConstraintSql = this.queryGenerator.dropConstraintQuery(tableName, results0[0].name);\n      await this.sequelize.query(dropConstraintSql, options);\n    }\n    const findForeignKeySql = this.queryGenerator.getForeignKeyQuery(tableName, attributeName);\n    const [results] = await this.sequelize.query(findForeignKeySql, options);\n    if (results.length) {\n      // No foreign key constraints found, so we can remove the column\n      const dropForeignKeySql = this.queryGenerator.dropForeignKeyQuery(tableName, results[0].constraint_name);\n      await this.sequelize.query(dropForeignKeySql, options);\n    }\n    //Check if the current column is a primaryKey\n    const primaryKeyConstraintSql = this.queryGenerator.getPrimaryKeyConstraintQuery(tableName, attributeName);\n    const [result] = await this.sequelize.query(primaryKeyConstraintSql, options);\n    if (result.length) {\n      const dropConstraintSql = this.queryGenerator.dropConstraintQuery(tableName, result[0].constraintName);\n      await this.sequelize.query(dropConstraintSql, options);\n    }\n    const removeSql = this.queryGenerator.removeColumnQuery(tableName, attributeName);\n    return this.sequelize.query(removeSql, options);\n  }\n\n  /**\n   * @override\n   */\n  async upsert(tableName, insertValues, updateValues, where, options) {\n    const model = options.model;\n    const wheres = [];\n\n    options = { ...options };\n\n    if (!Utils.isWhereEmpty(where)) {\n      wheres.push(where);\n    }\n\n    // Lets combine unique keys and indexes into one\n    let indexes = Object.values(model.uniqueKeys).map(item => item.fields);\n    indexes = indexes.concat(Object.values(model._indexes).filter(item => item.unique).map(item => item.fields));\n\n    const attributes = Object.keys(insertValues);\n    for (const index of indexes) {\n      if (_.intersection(attributes, index).length === index.length) {\n        where = {};\n        for (const field of index) {\n          where[field] = insertValues[field];\n        }\n        wheres.push(where);\n      }\n    }\n\n    where = { [Op.or]: wheres };\n\n    options.type = QueryTypes.UPSERT;\n    options.raw = true;\n\n    const sql = this.queryGenerator.upsertQuery(tableName, insertValues, updateValues, where, model, options);\n    return await this.sequelize.query(sql, options);\n  }\n}\n\nexports.MSSqlQueryInterface = MSSqlQueryInterface;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,MAAM,IAAI,QAAQ;AAElB,MAAM,QAAQ,QAAQ;AACtB,MAAM,aAAa,QAAQ;AAC3B,MAAM,KAAK,QAAQ;AACnB,MAAM,EAAE,mBAAmB,QAAQ;AAKnC,kCAAkC,eAAe;AAAA,QAMzC,aAAa,WAAW,eAAe,SAAS;AACpD,cAAU,iBAAE,KAAK,QAAS,WAAW;AAErC,UAAM,oBAAoB,KAAK,eAAe,0BAA0B,WAAW;AACnF,UAAM,CAAC,YAAY,MAAM,KAAK,UAAU,MAAM,mBAAmB;AACjE,QAAI,SAAS,QAAQ;AAEnB,YAAM,oBAAoB,KAAK,eAAe,oBAAoB,WAAW,SAAS,GAAG;AACzF,YAAM,KAAK,UAAU,MAAM,mBAAmB;AAAA;AAEhD,UAAM,oBAAoB,KAAK,eAAe,mBAAmB,WAAW;AAC5E,UAAM,CAAC,WAAW,MAAM,KAAK,UAAU,MAAM,mBAAmB;AAChE,QAAI,QAAQ,QAAQ;AAElB,YAAM,oBAAoB,KAAK,eAAe,oBAAoB,WAAW,QAAQ,GAAG;AACxF,YAAM,KAAK,UAAU,MAAM,mBAAmB;AAAA;AAGhD,UAAM,0BAA0B,KAAK,eAAe,6BAA6B,WAAW;AAC5F,UAAM,CAAC,UAAU,MAAM,KAAK,UAAU,MAAM,yBAAyB;AACrE,QAAI,OAAO,QAAQ;AACjB,YAAM,oBAAoB,KAAK,eAAe,oBAAoB,WAAW,OAAO,GAAG;AACvF,YAAM,KAAK,UAAU,MAAM,mBAAmB;AAAA;AAEhD,UAAM,YAAY,KAAK,eAAe,kBAAkB,WAAW;AACnE,WAAO,KAAK,UAAU,MAAM,WAAW;AAAA;AAAA,QAMnC,OAAO,WAAW,cAAc,cAAc,OAAO,SAAS;AAClE,UAAM,QAAQ,QAAQ;AACtB,UAAM,SAAS;AAEf,cAAU,mBAAK;AAEf,QAAI,CAAC,MAAM,aAAa,QAAQ;AAC9B,aAAO,KAAK;AAAA;AAId,QAAI,UAAU,OAAO,OAAO,MAAM,YAAY,IAAI,UAAQ,KAAK;AAC/D,cAAU,QAAQ,OAAO,OAAO,OAAO,MAAM,UAAU,OAAO,UAAQ,KAAK,QAAQ,IAAI,UAAQ,KAAK;AAEpG,UAAM,aAAa,OAAO,KAAK;AAC/B,eAAW,SAAS,SAAS;AAC3B,UAAI,EAAE,aAAa,YAAY,OAAO,WAAW,MAAM,QAAQ;AAC7D,gBAAQ;AACR,mBAAW,SAAS,OAAO;AACzB,gBAAM,SAAS,aAAa;AAAA;AAE9B,eAAO,KAAK;AAAA;AAAA;AAIhB,YAAQ,GAAG,GAAG,KAAK;AAEnB,YAAQ,OAAO,WAAW;AAC1B,YAAQ,MAAM;AAEd,UAAM,MAAM,KAAK,eAAe,YAAY,WAAW,cAAc,cAAc,OAAO,OAAO;AACjG,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA;AAI3C,QAAQ,sBAAsB;", "names": []}