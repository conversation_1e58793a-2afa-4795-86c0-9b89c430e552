{"version": 3, "sources": ["../../../src/dialects/mysql/query-generator.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst Utils = require('../../utils');\nconst AbstractQueryGenerator = require('../abstract/query-generator');\nconst util = require('util');\nconst Op = require('../../operators');\n\n\nconst JSON_FUNCTION_REGEX = /^\\s*((?:[a-z]+_){0,2}jsonb?(?:_[a-z]+){0,2})\\([^)]*\\)/i;\nconst JSON_OPERATOR_REGEX = /^\\s*(->>?|@>|<@|\\?[|&]?|\\|{2}|#-)/i;\nconst TOKEN_CAPTURE_REGEX = /^\\s*((?:([`\"'])(?:(?!\\2).|\\2{2})*\\2)|[\\w\\d\\s]+|[().,;+-])/i;\nconst FOREIGN_KEY_FIELDS = [\n  'CONSTRAINT_NAME as constraint_name',\n  'CONSTRAINT_NAME as constraintName',\n  'CONSTRAINT_SCHEMA as constraintSchema',\n  'CONSTRAINT_SCHEMA as constraintCatalog',\n  'TABLE_NAME as tableName',\n  'TABLE_SCHEMA as tableSchema',\n  'TABLE_SCHEMA as tableCatalog',\n  'COLUMN_NAME as columnName',\n  'REFERENCED_TABLE_SCHEMA as referencedTableSchema',\n  'REFERENCED_TABLE_SCHEMA as referencedTableCatalog',\n  'REFERENCED_TABLE_NAME as referencedTableName',\n  'REFERENCED_COLUMN_NAME as referencedColumnName'\n].join(',');\n\nconst typeWithoutDefault = new Set(['BLOB', 'TEXT', 'GEOMETRY', 'JSON']);\n\nclass MySQLQueryGenerator extends AbstractQueryGenerator {\n  constructor(options) {\n    super(options);\n\n    this.OperatorMap = {\n      ...this.OperatorMap,\n      [Op.regexp]: 'REGEXP',\n      [Op.notRegexp]: 'NOT REGEXP'\n    };\n  }\n\n  createDatabaseQuery(databaseName, options) {\n    options = {\n      charset: null,\n      collate: null,\n      ...options\n    };\n\n    return Utils.joinSQLFragments([\n      'CREATE DATABASE IF NOT EXISTS',\n      this.quoteIdentifier(databaseName),\n      options.charset && `DEFAULT CHARACTER SET ${this.escape(options.charset)}`,\n      options.collate && `DEFAULT COLLATE ${this.escape(options.collate)}`,\n      ';'\n    ]);\n  }\n\n  dropDatabaseQuery(databaseName) {\n    return `DROP DATABASE IF EXISTS ${this.quoteIdentifier(databaseName)};`;\n  }\n\n  createSchema() {\n    return 'SHOW TABLES';\n  }\n\n  showSchemasQuery() {\n    return 'SHOW TABLES';\n  }\n\n  versionQuery() {\n    return 'SELECT VERSION() as `version`';\n  }\n\n  createTableQuery(tableName, attributes, options) {\n    options = {\n      engine: 'InnoDB',\n      charset: null,\n      rowFormat: null,\n      ...options\n    };\n\n    const primaryKeys = [];\n    const foreignKeys = {};\n    const attrStr = [];\n\n    for (const attr in attributes) {\n      if (!Object.prototype.hasOwnProperty.call(attributes, attr)) continue;\n      const dataType = attributes[attr];\n      let match;\n\n      if (dataType.includes('PRIMARY KEY')) {\n        primaryKeys.push(attr);\n\n        if (dataType.includes('REFERENCES')) {\n          // MySQL doesn't support inline REFERENCES declarations: move to the end\n          match = dataType.match(/^(.+) (REFERENCES.*)$/);\n          attrStr.push(`${this.quoteIdentifier(attr)} ${match[1].replace('PRIMARY KEY', '')}`);\n          foreignKeys[attr] = match[2];\n        } else {\n          attrStr.push(`${this.quoteIdentifier(attr)} ${dataType.replace('PRIMARY KEY', '')}`);\n        }\n      } else if (dataType.includes('REFERENCES')) {\n        // MySQL doesn't support inline REFERENCES declarations: move to the end\n        match = dataType.match(/^(.+) (REFERENCES.*)$/);\n        attrStr.push(`${this.quoteIdentifier(attr)} ${match[1]}`);\n        foreignKeys[attr] = match[2];\n      } else {\n        attrStr.push(`${this.quoteIdentifier(attr)} ${dataType}`);\n      }\n    }\n\n    const table = this.quoteTable(tableName);\n    let attributesClause = attrStr.join(', ');\n    const pkString = primaryKeys.map(pk => this.quoteIdentifier(pk)).join(', ');\n\n    if (options.uniqueKeys) {\n      _.each(options.uniqueKeys, (columns, indexName) => {\n        if (columns.customIndex) {\n          if (typeof indexName !== 'string') {\n            indexName = `uniq_${tableName}_${columns.fields.join('_')}`;\n          }\n          attributesClause += `, UNIQUE ${this.quoteIdentifier(indexName)} (${columns.fields.map(field => this.quoteIdentifier(field)).join(', ')})`;\n        }\n      });\n    }\n\n    if (pkString.length > 0) {\n      attributesClause += `, PRIMARY KEY (${pkString})`;\n    }\n\n    for (const fkey in foreignKeys) {\n      if (Object.prototype.hasOwnProperty.call(foreignKeys, fkey)) {\n        attributesClause += `, FOREIGN KEY (${this.quoteIdentifier(fkey)}) ${foreignKeys[fkey]}`;\n      }\n    }\n\n    return Utils.joinSQLFragments([\n      'CREATE TABLE IF NOT EXISTS',\n      table,\n      `(${attributesClause})`,\n      `ENGINE=${options.engine}`,\n      options.comment && typeof options.comment === 'string' && `COMMENT ${this.escape(options.comment)}`,\n      options.charset && `DEFAULT CHARSET=${options.charset}`,\n      options.collate && `COLLATE ${options.collate}`,\n      options.initialAutoIncrement && `AUTO_INCREMENT=${options.initialAutoIncrement}`,\n      options.rowFormat && `ROW_FORMAT=${options.rowFormat}`,\n      ';'\n    ]);\n  }\n\n  describeTableQuery(tableName, schema, schemaDelimiter) {\n    const table = this.quoteTable(\n      this.addSchema({\n        tableName,\n        _schema: schema,\n        _schemaDelimiter: schemaDelimiter\n      })\n    );\n\n    return `SHOW FULL COLUMNS FROM ${table};`;\n  }\n\n  showTablesQuery(database) {\n    let query = 'SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = \\'BASE TABLE\\'';\n    if (database) {\n      query += ` AND TABLE_SCHEMA = ${this.escape(database)}`;\n    } else {\n      query += ' AND TABLE_SCHEMA NOT IN (\\'MYSQL\\', \\'INFORMATION_SCHEMA\\', \\'PERFORMANCE_SCHEMA\\', \\'SYS\\', \\'mysql\\', \\'information_schema\\', \\'performance_schema\\', \\'sys\\')';\n    }\n    return `${query};`;\n  }\n\n  tableExistsQuery(table) {\n    // remove first & last `, then escape as SQL string\n    const tableName = this.escape(this.quoteTable(table).slice(1, -1));\n\n    return `SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = ${tableName} AND TABLE_SCHEMA = ${this.escape(this.sequelize.config.database)}`;\n  }\n\n  addColumnQuery(table, key, dataType) {\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(table),\n      'ADD',\n      this.quoteIdentifier(key),\n      this.attributeToSQL(dataType, {\n        context: 'addColumn',\n        tableName: table,\n        foreignKey: key\n      }),\n      ';'\n    ]);\n  }\n\n  removeColumnQuery(tableName, attributeName) {\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      'DROP',\n      this.quoteIdentifier(attributeName),\n      ';'\n    ]);\n  }\n\n  changeColumnQuery(tableName, attributes) {\n    const attrString = [];\n    const constraintString = [];\n\n    for (const attributeName in attributes) {\n      let definition = attributes[attributeName];\n      if (definition.includes('REFERENCES')) {\n        const attrName = this.quoteIdentifier(attributeName);\n        definition = definition.replace(/.+?(?=REFERENCES)/, '');\n        constraintString.push(`FOREIGN KEY (${attrName}) ${definition}`);\n      } else {\n        attrString.push(`\\`${attributeName}\\` \\`${attributeName}\\` ${definition}`);\n      }\n    }\n\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      attrString.length && `CHANGE ${attrString.join(', ')}`,\n      constraintString.length && `ADD ${constraintString.join(', ')}`,\n      ';'\n    ]);\n  }\n\n  renameColumnQuery(tableName, attrBefore, attributes) {\n    const attrString = [];\n\n    for (const attrName in attributes) {\n      const definition = attributes[attrName];\n      attrString.push(`\\`${attrBefore}\\` \\`${attrName}\\` ${definition}`);\n    }\n\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      'CHANGE',\n      attrString.join(', '),\n      ';'\n    ]);\n  }\n\n  handleSequelizeMethod(smth, tableName, factory, options, prepend) {\n    if (smth instanceof Utils.Json) {\n      // Parse nested object\n      if (smth.conditions) {\n        const conditions = this.parseConditionObject(smth.conditions).map(condition =>\n          `${this.jsonPathExtractionQuery(condition.path[0], _.tail(condition.path))} = '${condition.value}'`\n        );\n\n        return conditions.join(' AND ');\n      }\n      if (smth.path) {\n        let str;\n\n        // Allow specifying conditions using the sqlite json functions\n        if (this._checkValidJsonStatement(smth.path)) {\n          str = smth.path;\n        } else {\n          // Also support json property accessors\n          const paths = _.toPath(smth.path);\n          const column = paths.shift();\n          str = this.jsonPathExtractionQuery(column, paths);\n        }\n\n        if (smth.value) {\n          str += util.format(' = %s', this.escape(smth.value));\n        }\n\n        return str;\n      }\n    } else if (smth instanceof Utils.Cast) {\n      if (/timestamp/i.test(smth.type)) {\n        smth.type = 'datetime';\n      } else if (smth.json && /boolean/i.test(smth.type)) {\n        // true or false cannot be casted as booleans within a JSON structure\n        smth.type = 'char';\n      } else if (/double precision/i.test(smth.type) || /boolean/i.test(smth.type) || /integer/i.test(smth.type)) {\n        smth.type = 'decimal';\n      } else if (/text/i.test(smth.type)) {\n        smth.type = 'char';\n      }\n    }\n\n    return super.handleSequelizeMethod(smth, tableName, factory, options, prepend);\n  }\n\n  _toJSONValue(value) {\n    // true/false are stored as strings in mysql\n    if (typeof value === 'boolean') {\n      return value.toString();\n    }\n    // null is stored as a string in mysql\n    if (value === null) {\n      return 'null';\n    }\n    return value;\n  }\n\n  truncateTableQuery(tableName) {\n    return `TRUNCATE ${this.quoteTable(tableName)}`;\n  }\n\n  deleteQuery(tableName, where, options = {}, model) {\n    let limit = '';\n    let query = `DELETE FROM ${this.quoteTable(tableName)}`;\n\n    if (options.limit) {\n      limit = ` LIMIT ${this.escape(options.limit)}`;\n    }\n\n    where = this.getWhereConditions(where, null, model, options);\n\n    if (where) {\n      query += ` WHERE ${where}`;\n    }\n\n    return query + limit;\n  }\n\n  showIndexesQuery(tableName, options) {\n    return Utils.joinSQLFragments([\n      `SHOW INDEX FROM ${this.quoteTable(tableName)}`,\n      options && options.database && `FROM \\`${options.database}\\``\n    ]);\n  }\n\n  showConstraintsQuery(table, constraintName) {\n    const tableName = table.tableName || table;\n    const schemaName = table.schema;\n\n    return Utils.joinSQLFragments([\n      'SELECT CONSTRAINT_CATALOG AS constraintCatalog,',\n      'CONSTRAINT_NAME AS constraintName,',\n      'CONSTRAINT_SCHEMA AS constraintSchema,',\n      'CONSTRAINT_TYPE AS constraintType,',\n      'TABLE_NAME AS tableName,',\n      'TABLE_SCHEMA AS tableSchema',\n      'from INFORMATION_SCHEMA.TABLE_CONSTRAINTS',\n      `WHERE table_name='${tableName}'`,\n      constraintName && `AND constraint_name = '${constraintName}'`,\n      schemaName && `AND TABLE_SCHEMA = '${schemaName}'`,\n      ';'\n    ]);\n  }\n\n  removeIndexQuery(tableName, indexNameOrAttributes) {\n    let indexName = indexNameOrAttributes;\n\n    if (typeof indexName !== 'string') {\n      indexName = Utils.underscore(`${tableName}_${indexNameOrAttributes.join('_')}`);\n    }\n\n    return Utils.joinSQLFragments([\n      'DROP INDEX',\n      this.quoteIdentifier(indexName),\n      'ON',\n      this.quoteTable(tableName)\n    ]);\n  }\n\n  attributeToSQL(attribute, options) {\n    if (!_.isPlainObject(attribute)) {\n      attribute = {\n        type: attribute\n      };\n    }\n\n    const attributeString = attribute.type.toString({ escape: this.escape.bind(this) });\n    let template = attributeString;\n\n    if (attribute.allowNull === false) {\n      template += ' NOT NULL';\n    }\n\n    if (attribute.autoIncrement) {\n      template += ' auto_increment';\n    }\n\n    // BLOB/TEXT/GEOMETRY/JSON cannot have a default value\n    if (!typeWithoutDefault.has(attributeString)\n      && attribute.type._binary !== true\n      && Utils.defaultValueSchemable(attribute.defaultValue)) {\n      template += ` DEFAULT ${this.escape(attribute.defaultValue)}`;\n    }\n\n    if (attribute.unique === true) {\n      template += ' UNIQUE';\n    }\n\n    if (attribute.primaryKey) {\n      template += ' PRIMARY KEY';\n    }\n\n    if (attribute.comment) {\n      template += ` COMMENT ${this.escape(attribute.comment)}`;\n    }\n\n    if (attribute.first) {\n      template += ' FIRST';\n    }\n    if (attribute.after) {\n      template += ` AFTER ${this.quoteIdentifier(attribute.after)}`;\n    }\n\n    if ((!options || !options.withoutForeignKeyConstraints) && attribute.references) {\n      if (options && options.context === 'addColumn' && options.foreignKey) {\n        const attrName = this.quoteIdentifier(options.foreignKey);\n        const fkName = this.quoteIdentifier(`${options.tableName}_${attrName}_foreign_idx`);\n\n        template += `, ADD CONSTRAINT ${fkName} FOREIGN KEY (${attrName})`;\n      }\n\n      template += ` REFERENCES ${this.quoteTable(attribute.references.model)}`;\n\n      if (attribute.references.key) {\n        template += ` (${this.quoteIdentifier(attribute.references.key)})`;\n      } else {\n        template += ` (${this.quoteIdentifier('id')})`;\n      }\n\n      if (attribute.onDelete) {\n        template += ` ON DELETE ${attribute.onDelete.toUpperCase()}`;\n      }\n\n      if (attribute.onUpdate) {\n        template += ` ON UPDATE ${attribute.onUpdate.toUpperCase()}`;\n      }\n    }\n\n    return template;\n  }\n\n  attributesToSQL(attributes, options) {\n    const result = {};\n\n    for (const key in attributes) {\n      const attribute = attributes[key];\n      result[attribute.field || key] = this.attributeToSQL(attribute, options);\n    }\n\n    return result;\n  }\n\n  /**\n   * Check whether the statmement is json function or simple path\n   *\n   * @param   {string}  stmt  The statement to validate\n   * @returns {boolean}       true if the given statement is json function\n   * @throws  {Error}         throw if the statement looks like json function but has invalid token\n   * @private\n   */\n  _checkValidJsonStatement(stmt) {\n    if (typeof stmt !== 'string') {\n      return false;\n    }\n\n    let currentIndex = 0;\n    let openingBrackets = 0;\n    let closingBrackets = 0;\n    let hasJsonFunction = false;\n    let hasInvalidToken = false;\n\n    while (currentIndex < stmt.length) {\n      const string = stmt.substr(currentIndex);\n      const functionMatches = JSON_FUNCTION_REGEX.exec(string);\n      if (functionMatches) {\n        currentIndex += functionMatches[0].indexOf('(');\n        hasJsonFunction = true;\n        continue;\n      }\n\n      const operatorMatches = JSON_OPERATOR_REGEX.exec(string);\n      if (operatorMatches) {\n        currentIndex += operatorMatches[0].length;\n        hasJsonFunction = true;\n        continue;\n      }\n\n      const tokenMatches = TOKEN_CAPTURE_REGEX.exec(string);\n      if (tokenMatches) {\n        const capturedToken = tokenMatches[1];\n        if (capturedToken === '(') {\n          openingBrackets++;\n        } else if (capturedToken === ')') {\n          closingBrackets++;\n        } else if (capturedToken === ';') {\n          hasInvalidToken = true;\n          break;\n        }\n        currentIndex += tokenMatches[0].length;\n        continue;\n      }\n\n      break;\n    }\n\n    // Check invalid json statement\n    if (hasJsonFunction && (hasInvalidToken || openingBrackets !== closingBrackets)) {\n      throw new Error(`Invalid json statement: ${stmt}`);\n    }\n\n    // return true if the statement has valid json function\n    return hasJsonFunction;\n  }\n\n  /**\n   * Generates an SQL query that returns all foreign keys of a table.\n   *\n   * @param  {object} table  The table.\n   * @param  {string} schemaName The name of the schema.\n   * @returns {string}            The generated sql query.\n   * @private\n   */\n  getForeignKeysQuery(table, schemaName) {\n    const tableName = table.tableName || table;\n    return Utils.joinSQLFragments([\n      'SELECT',\n      FOREIGN_KEY_FIELDS,\n      `FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = '${tableName}'`,\n      `AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='${schemaName}'`,\n      'AND REFERENCED_TABLE_NAME IS NOT NULL',\n      ';'\n    ]);\n  }\n\n  /**\n   * Generates an SQL query that returns the foreign key constraint of a given column.\n   *\n   * @param  {object} table  The table.\n   * @param  {string} columnName The name of the column.\n   * @returns {string}            The generated sql query.\n   * @private\n   */\n  getForeignKeyQuery(table, columnName) {\n    const quotedSchemaName = table.schema ? wrapSingleQuote(table.schema) : '';\n    const quotedTableName = wrapSingleQuote(table.tableName || table);\n    const quotedColumnName = wrapSingleQuote(columnName);\n\n    return Utils.joinSQLFragments([\n      'SELECT',\n      FOREIGN_KEY_FIELDS,\n      'FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE',\n      'WHERE (',\n      [\n        `REFERENCED_TABLE_NAME = ${quotedTableName}`,\n        table.schema && `AND REFERENCED_TABLE_SCHEMA = ${quotedSchemaName}`,\n        `AND REFERENCED_COLUMN_NAME = ${quotedColumnName}`\n      ],\n      ') OR (',\n      [\n        `TABLE_NAME = ${quotedTableName}`,\n        table.schema && `AND TABLE_SCHEMA = ${quotedSchemaName}`,\n        `AND COLUMN_NAME = ${quotedColumnName}`,\n        'AND REFERENCED_TABLE_NAME IS NOT NULL'\n      ],\n      ')'\n    ]);\n  }\n\n  /**\n   * Generates an SQL query that removes a foreign key from a table.\n   *\n   * @param  {string} tableName  The name of the table.\n   * @param  {string} foreignKey The name of the foreign key constraint.\n   * @returns {string}            The generated sql query.\n   * @private\n   */\n  dropForeignKeyQuery(tableName, foreignKey) {\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      'DROP FOREIGN KEY',\n      this.quoteIdentifier(foreignKey),\n      ';'\n    ]);\n  }\n\n  /**\n   * Quote identifier in sql clause\n   *\n   * @param {string} identifier\n   * @param {boolean} force\n   *\n   * @returns {string}\n   */\n  quoteIdentifier(identifier, force) {\n    return Utils.addTicks(Utils.removeTicks(identifier, '`'), '`');\n  }\n}\n\n// private methods\nfunction wrapSingleQuote(identifier) {\n  return Utils.addTicks(identifier, '\\'');\n}\n\nmodule.exports = MySQLQueryGenerator;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,QAAQ,QAAQ;AACtB,MAAM,yBAAyB,QAAQ;AACvC,MAAM,OAAO,QAAQ;AACrB,MAAM,KAAK,QAAQ;AAGnB,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAC5B,MAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,KAAK;AAEP,MAAM,qBAAqB,oBAAI,IAAI,CAAC,QAAQ,QAAQ,YAAY;AAEhE,kCAAkC,uBAAuB;AAAA,EACvD,YAAY,SAAS;AACnB,UAAM;AAEN,SAAK,cAAc,iCACd,KAAK,cADS;AAAA,OAEhB,GAAG,SAAS;AAAA,OACZ,GAAG,YAAY;AAAA;AAAA;AAAA,EAIpB,oBAAoB,cAAc,SAAS;AACzC,cAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,OACN;AAGL,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,QAAQ,WAAW,yBAAyB,KAAK,OAAO,QAAQ;AAAA,MAChE,QAAQ,WAAW,mBAAmB,KAAK,OAAO,QAAQ;AAAA,MAC1D;AAAA;AAAA;AAAA,EAIJ,kBAAkB,cAAc;AAC9B,WAAO,2BAA2B,KAAK,gBAAgB;AAAA;AAAA,EAGzD,eAAe;AACb,WAAO;AAAA;AAAA,EAGT,mBAAmB;AACjB,WAAO;AAAA;AAAA,EAGT,eAAe;AACb,WAAO;AAAA;AAAA,EAGT,iBAAiB,WAAW,YAAY,SAAS;AAC/C,cAAU;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,OACR;AAGL,UAAM,cAAc;AACpB,UAAM,cAAc;AACpB,UAAM,UAAU;AAEhB,eAAW,QAAQ,YAAY;AAC7B,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,YAAY;AAAO;AAC7D,YAAM,WAAW,WAAW;AAC5B,UAAI;AAEJ,UAAI,SAAS,SAAS,gBAAgB;AACpC,oBAAY,KAAK;AAEjB,YAAI,SAAS,SAAS,eAAe;AAEnC,kBAAQ,SAAS,MAAM;AACvB,kBAAQ,KAAK,GAAG,KAAK,gBAAgB,SAAS,MAAM,GAAG,QAAQ,eAAe;AAC9E,sBAAY,QAAQ,MAAM;AAAA,eACrB;AACL,kBAAQ,KAAK,GAAG,KAAK,gBAAgB,SAAS,SAAS,QAAQ,eAAe;AAAA;AAAA,iBAEvE,SAAS,SAAS,eAAe;AAE1C,gBAAQ,SAAS,MAAM;AACvB,gBAAQ,KAAK,GAAG,KAAK,gBAAgB,SAAS,MAAM;AACpD,oBAAY,QAAQ,MAAM;AAAA,aACrB;AACL,gBAAQ,KAAK,GAAG,KAAK,gBAAgB,SAAS;AAAA;AAAA;AAIlD,UAAM,QAAQ,KAAK,WAAW;AAC9B,QAAI,mBAAmB,QAAQ,KAAK;AACpC,UAAM,WAAW,YAAY,IAAI,QAAM,KAAK,gBAAgB,KAAK,KAAK;AAEtE,QAAI,QAAQ,YAAY;AACtB,QAAE,KAAK,QAAQ,YAAY,CAAC,SAAS,cAAc;AACjD,YAAI,QAAQ,aAAa;AACvB,cAAI,OAAO,cAAc,UAAU;AACjC,wBAAY,QAAQ,aAAa,QAAQ,OAAO,KAAK;AAAA;AAEvD,8BAAoB,YAAY,KAAK,gBAAgB,eAAe,QAAQ,OAAO,IAAI,WAAS,KAAK,gBAAgB,QAAQ,KAAK;AAAA;AAAA;AAAA;AAKxI,QAAI,SAAS,SAAS,GAAG;AACvB,0BAAoB,kBAAkB;AAAA;AAGxC,eAAW,QAAQ,aAAa;AAC9B,UAAI,OAAO,UAAU,eAAe,KAAK,aAAa,OAAO;AAC3D,4BAAoB,kBAAkB,KAAK,gBAAgB,UAAU,YAAY;AAAA;AAAA;AAIrF,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,IAAI;AAAA,MACJ,UAAU,QAAQ;AAAA,MAClB,QAAQ,WAAW,OAAO,QAAQ,YAAY,YAAY,WAAW,KAAK,OAAO,QAAQ;AAAA,MACzF,QAAQ,WAAW,mBAAmB,QAAQ;AAAA,MAC9C,QAAQ,WAAW,WAAW,QAAQ;AAAA,MACtC,QAAQ,wBAAwB,kBAAkB,QAAQ;AAAA,MAC1D,QAAQ,aAAa,cAAc,QAAQ;AAAA,MAC3C;AAAA;AAAA;AAAA,EAIJ,mBAAmB,WAAW,QAAQ,iBAAiB;AACrD,UAAM,QAAQ,KAAK,WACjB,KAAK,UAAU;AAAA,MACb;AAAA,MACA,SAAS;AAAA,MACT,kBAAkB;AAAA;AAItB,WAAO,0BAA0B;AAAA;AAAA,EAGnC,gBAAgB,UAAU;AACxB,QAAI,QAAQ;AACZ,QAAI,UAAU;AACZ,eAAS,uBAAuB,KAAK,OAAO;AAAA,WACvC;AACL,eAAS;AAAA;AAEX,WAAO,GAAG;AAAA;AAAA,EAGZ,iBAAiB,OAAO;AAEtB,UAAM,YAAY,KAAK,OAAO,KAAK,WAAW,OAAO,MAAM,GAAG;AAE9D,WAAO,qGAAqG,gCAAgC,KAAK,OAAO,KAAK,UAAU,OAAO;AAAA;AAAA,EAGhL,eAAe,OAAO,KAAK,UAAU;AACnC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK,eAAe,UAAU;AAAA,QAC5B,SAAS;AAAA,QACT,WAAW;AAAA,QACX,YAAY;AAAA;AAAA,MAEd;AAAA;AAAA;AAAA,EAIJ,kBAAkB,WAAW,eAAe;AAC1C,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB;AAAA;AAAA;AAAA,EAIJ,kBAAkB,WAAW,YAAY;AACvC,UAAM,aAAa;AACnB,UAAM,mBAAmB;AAEzB,eAAW,iBAAiB,YAAY;AACtC,UAAI,aAAa,WAAW;AAC5B,UAAI,WAAW,SAAS,eAAe;AACrC,cAAM,WAAW,KAAK,gBAAgB;AACtC,qBAAa,WAAW,QAAQ,qBAAqB;AACrD,yBAAiB,KAAK,gBAAgB,aAAa;AAAA,aAC9C;AACL,mBAAW,KAAK,KAAK,qBAAqB,mBAAmB;AAAA;AAAA;AAIjE,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB,WAAW,UAAU,UAAU,WAAW,KAAK;AAAA,MAC/C,iBAAiB,UAAU,OAAO,iBAAiB,KAAK;AAAA,MACxD;AAAA;AAAA;AAAA,EAIJ,kBAAkB,WAAW,YAAY,YAAY;AACnD,UAAM,aAAa;AAEnB,eAAW,YAAY,YAAY;AACjC,YAAM,aAAa,WAAW;AAC9B,iBAAW,KAAK,KAAK,kBAAkB,cAAc;AAAA;AAGvD,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,WAAW,KAAK;AAAA,MAChB;AAAA;AAAA;AAAA,EAIJ,sBAAsB,MAAM,WAAW,SAAS,SAAS,SAAS;AAChE,QAAI,gBAAgB,MAAM,MAAM;AAE9B,UAAI,KAAK,YAAY;AACnB,cAAM,aAAa,KAAK,qBAAqB,KAAK,YAAY,IAAI,eAChE,GAAG,KAAK,wBAAwB,UAAU,KAAK,IAAI,EAAE,KAAK,UAAU,aAAa,UAAU;AAG7F,eAAO,WAAW,KAAK;AAAA;AAEzB,UAAI,KAAK,MAAM;AACb,YAAI;AAGJ,YAAI,KAAK,yBAAyB,KAAK,OAAO;AAC5C,gBAAM,KAAK;AAAA,eACN;AAEL,gBAAM,QAAQ,EAAE,OAAO,KAAK;AAC5B,gBAAM,SAAS,MAAM;AACrB,gBAAM,KAAK,wBAAwB,QAAQ;AAAA;AAG7C,YAAI,KAAK,OAAO;AACd,iBAAO,KAAK,OAAO,SAAS,KAAK,OAAO,KAAK;AAAA;AAG/C,eAAO;AAAA;AAAA,eAEA,gBAAgB,MAAM,MAAM;AACrC,UAAI,aAAa,KAAK,KAAK,OAAO;AAChC,aAAK,OAAO;AAAA,iBACH,KAAK,QAAQ,WAAW,KAAK,KAAK,OAAO;AAElD,aAAK,OAAO;AAAA,iBACH,oBAAoB,KAAK,KAAK,SAAS,WAAW,KAAK,KAAK,SAAS,WAAW,KAAK,KAAK,OAAO;AAC1G,aAAK,OAAO;AAAA,iBACH,QAAQ,KAAK,KAAK,OAAO;AAClC,aAAK,OAAO;AAAA;AAAA;AAIhB,WAAO,MAAM,sBAAsB,MAAM,WAAW,SAAS,SAAS;AAAA;AAAA,EAGxE,aAAa,OAAO;AAElB,QAAI,OAAO,UAAU,WAAW;AAC9B,aAAO,MAAM;AAAA;AAGf,QAAI,UAAU,MAAM;AAClB,aAAO;AAAA;AAET,WAAO;AAAA;AAAA,EAGT,mBAAmB,WAAW;AAC5B,WAAO,YAAY,KAAK,WAAW;AAAA;AAAA,EAGrC,YAAY,WAAW,OAAO,UAAU,IAAI,OAAO;AACjD,QAAI,QAAQ;AACZ,QAAI,QAAQ,eAAe,KAAK,WAAW;AAE3C,QAAI,QAAQ,OAAO;AACjB,cAAQ,UAAU,KAAK,OAAO,QAAQ;AAAA;AAGxC,YAAQ,KAAK,mBAAmB,OAAO,MAAM,OAAO;AAEpD,QAAI,OAAO;AACT,eAAS,UAAU;AAAA;AAGrB,WAAO,QAAQ;AAAA;AAAA,EAGjB,iBAAiB,WAAW,SAAS;AACnC,WAAO,MAAM,iBAAiB;AAAA,MAC5B,mBAAmB,KAAK,WAAW;AAAA,MACnC,WAAW,QAAQ,YAAY,UAAU,QAAQ;AAAA;AAAA;AAAA,EAIrD,qBAAqB,OAAO,gBAAgB;AAC1C,UAAM,YAAY,MAAM,aAAa;AACrC,UAAM,aAAa,MAAM;AAEzB,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,qBAAqB;AAAA,MACrB,kBAAkB,0BAA0B;AAAA,MAC5C,cAAc,uBAAuB;AAAA,MACrC;AAAA;AAAA;AAAA,EAIJ,iBAAiB,WAAW,uBAAuB;AACjD,QAAI,YAAY;AAEhB,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,MAAM,WAAW,GAAG,aAAa,sBAAsB,KAAK;AAAA;AAG1E,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB;AAAA,MACA,KAAK,WAAW;AAAA;AAAA;AAAA,EAIpB,eAAe,WAAW,SAAS;AACjC,QAAI,CAAC,EAAE,cAAc,YAAY;AAC/B,kBAAY;AAAA,QACV,MAAM;AAAA;AAAA;AAIV,UAAM,kBAAkB,UAAU,KAAK,SAAS,EAAE,QAAQ,KAAK,OAAO,KAAK;AAC3E,QAAI,WAAW;AAEf,QAAI,UAAU,cAAc,OAAO;AACjC,kBAAY;AAAA;AAGd,QAAI,UAAU,eAAe;AAC3B,kBAAY;AAAA;AAId,QAAI,CAAC,mBAAmB,IAAI,oBACvB,UAAU,KAAK,YAAY,QAC3B,MAAM,sBAAsB,UAAU,eAAe;AACxD,kBAAY,YAAY,KAAK,OAAO,UAAU;AAAA;AAGhD,QAAI,UAAU,WAAW,MAAM;AAC7B,kBAAY;AAAA;AAGd,QAAI,UAAU,YAAY;AACxB,kBAAY;AAAA;AAGd,QAAI,UAAU,SAAS;AACrB,kBAAY,YAAY,KAAK,OAAO,UAAU;AAAA;AAGhD,QAAI,UAAU,OAAO;AACnB,kBAAY;AAAA;AAEd,QAAI,UAAU,OAAO;AACnB,kBAAY,UAAU,KAAK,gBAAgB,UAAU;AAAA;AAGvD,QAAK,EAAC,WAAW,CAAC,QAAQ,iCAAiC,UAAU,YAAY;AAC/E,UAAI,WAAW,QAAQ,YAAY,eAAe,QAAQ,YAAY;AACpE,cAAM,WAAW,KAAK,gBAAgB,QAAQ;AAC9C,cAAM,SAAS,KAAK,gBAAgB,GAAG,QAAQ,aAAa;AAE5D,oBAAY,oBAAoB,uBAAuB;AAAA;AAGzD,kBAAY,eAAe,KAAK,WAAW,UAAU,WAAW;AAEhE,UAAI,UAAU,WAAW,KAAK;AAC5B,oBAAY,KAAK,KAAK,gBAAgB,UAAU,WAAW;AAAA,aACtD;AACL,oBAAY,KAAK,KAAK,gBAAgB;AAAA;AAGxC,UAAI,UAAU,UAAU;AACtB,oBAAY,cAAc,UAAU,SAAS;AAAA;AAG/C,UAAI,UAAU,UAAU;AACtB,oBAAY,cAAc,UAAU,SAAS;AAAA;AAAA;AAIjD,WAAO;AAAA;AAAA,EAGT,gBAAgB,YAAY,SAAS;AACnC,UAAM,SAAS;AAEf,eAAW,OAAO,YAAY;AAC5B,YAAM,YAAY,WAAW;AAC7B,aAAO,UAAU,SAAS,OAAO,KAAK,eAAe,WAAW;AAAA;AAGlE,WAAO;AAAA;AAAA,EAWT,yBAAyB,MAAM;AAC7B,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO;AAAA;AAGT,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AAEtB,WAAO,eAAe,KAAK,QAAQ;AACjC,YAAM,SAAS,KAAK,OAAO;AAC3B,YAAM,kBAAkB,oBAAoB,KAAK;AACjD,UAAI,iBAAiB;AACnB,wBAAgB,gBAAgB,GAAG,QAAQ;AAC3C,0BAAkB;AAClB;AAAA;AAGF,YAAM,kBAAkB,oBAAoB,KAAK;AACjD,UAAI,iBAAiB;AACnB,wBAAgB,gBAAgB,GAAG;AACnC,0BAAkB;AAClB;AAAA;AAGF,YAAM,eAAe,oBAAoB,KAAK;AAC9C,UAAI,cAAc;AAChB,cAAM,gBAAgB,aAAa;AACnC,YAAI,kBAAkB,KAAK;AACzB;AAAA,mBACS,kBAAkB,KAAK;AAChC;AAAA,mBACS,kBAAkB,KAAK;AAChC,4BAAkB;AAClB;AAAA;AAEF,wBAAgB,aAAa,GAAG;AAChC;AAAA;AAGF;AAAA;AAIF,QAAI,mBAAoB,oBAAmB,oBAAoB,kBAAkB;AAC/E,YAAM,IAAI,MAAM,2BAA2B;AAAA;AAI7C,WAAO;AAAA;AAAA,EAWT,oBAAoB,OAAO,YAAY;AACrC,UAAM,YAAY,MAAM,aAAa;AACrC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,gEAAgE;AAAA,MAChE,yDAAyD;AAAA,MACzD;AAAA,MACA;AAAA;AAAA;AAAA,EAYJ,mBAAmB,OAAO,YAAY;AACpC,UAAM,mBAAmB,MAAM,SAAS,gBAAgB,MAAM,UAAU;AACxE,UAAM,kBAAkB,gBAAgB,MAAM,aAAa;AAC3D,UAAM,mBAAmB,gBAAgB;AAEzC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,2BAA2B;AAAA,QAC3B,MAAM,UAAU,iCAAiC;AAAA,QACjD,gCAAgC;AAAA;AAAA,MAElC;AAAA,MACA;AAAA,QACE,gBAAgB;AAAA,QAChB,MAAM,UAAU,sBAAsB;AAAA,QACtC,qBAAqB;AAAA,QACrB;AAAA;AAAA,MAEF;AAAA;AAAA;AAAA,EAYJ,oBAAoB,WAAW,YAAY;AACzC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB;AAAA;AAAA;AAAA,EAYJ,gBAAgB,YAAY,OAAO;AACjC,WAAO,MAAM,SAAS,MAAM,YAAY,YAAY,MAAM;AAAA;AAAA;AAK9D,yBAAyB,YAAY;AACnC,SAAO,MAAM,SAAS,YAAY;AAAA;AAGpC,OAAO,UAAU;", "names": []}