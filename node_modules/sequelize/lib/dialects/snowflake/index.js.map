{"version": 3, "sources": ["../../../src/dialects/snowflake/index.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst AbstractDialect = require('../abstract');\nconst ConnectionManager = require('./connection-manager');\nconst Query = require('./query');\nconst QueryGenerator = require('./query-generator');\nconst DataTypes = require('../../data-types').snowflake;\nconst { SnowflakeQueryInterface } = require('./query-interface');\n\nclass SnowflakeDialect extends AbstractDialect {\n  constructor(sequelize) {\n    super();\n    this.sequelize = sequelize;\n    this.connectionManager = new ConnectionManager(this, sequelize);\n    this.queryGenerator = new QueryGenerator({\n      _dialect: this,\n      sequelize\n    });\n    this.queryInterface = new SnowflakeQueryInterface(sequelize, this.queryGenerator);\n  }\n}\n\nSnowflakeDialect.prototype.supports = _.merge(_.cloneDeep(AbstractDialect.prototype.supports), {\n  'VALUES ()': true,\n  'LIMIT ON UPDATE': true,\n  lock: true,\n  forShare: 'LOCK IN SHARE MODE',\n  settingIsolationLevelDuringTransaction: false,\n  inserts: {\n    ignoreDuplicates: ' IGNORE',\n    // disable for now, but could be enable by approach below\n    // https://stackoverflow.com/questions/54828745/how-to-migrate-on-conflict-do-nothing-from-postgresql-to-snowflake\n    updateOnDuplicate: false\n  },\n  index: {\n    collate: false,\n    length: true,\n    parser: true,\n    type: true,\n    using: 1\n  },\n  constraints: {\n    dropConstraint: false,\n    check: false\n  },\n  indexViaAlter: true,\n  indexHints: true,\n  NUMERIC: true,\n  // disable for now, need more work to enable the GEOGRAPHY MAPPING\n  GEOMETRY: false,\n  JSON: false,\n  REGEXP: true,\n  schemas: true\n});\n\nSnowflakeDialect.prototype.defaultVersion = '5.7.0';\nSnowflakeDialect.prototype.Query = Query;\nSnowflakeDialect.prototype.QueryGenerator = QueryGenerator;\nSnowflakeDialect.prototype.DataTypes = DataTypes;\nSnowflakeDialect.prototype.name = 'snowflake';\nSnowflakeDialect.prototype.TICK_CHAR = '\"';\nSnowflakeDialect.prototype.TICK_CHAR_LEFT = SnowflakeDialect.prototype.TICK_CHAR;\nSnowflakeDialect.prototype.TICK_CHAR_RIGHT = SnowflakeDialect.prototype.TICK_CHAR;\n\nmodule.exports = SnowflakeDialect;\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,kBAAkB,QAAQ;AAChC,MAAM,oBAAoB,QAAQ;AAClC,MAAM,QAAQ,QAAQ;AACtB,MAAM,iBAAiB,QAAQ;AAC/B,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,EAAE,4BAA4B,QAAQ;AAE5C,+BAA+B,gBAAgB;AAAA,EAC7C,YAAY,WAAW;AACrB;AACA,SAAK,YAAY;AACjB,SAAK,oBAAoB,IAAI,kBAAkB,MAAM;AACrD,SAAK,iBAAiB,IAAI,eAAe;AAAA,MACvC,UAAU;AAAA,MACV;AAAA;AAEF,SAAK,iBAAiB,IAAI,wBAAwB,WAAW,KAAK;AAAA;AAAA;AAItE,iBAAiB,UAAU,WAAW,EAAE,MAAM,EAAE,UAAU,gBAAgB,UAAU,WAAW;AAAA,EAC7F,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,MAAM;AAAA,EACN,UAAU;AAAA,EACV,wCAAwC;AAAA,EACxC,SAAS;AAAA,IACP,kBAAkB;AAAA,IAGlB,mBAAmB;AAAA;AAAA,EAErB,OAAO;AAAA,IACL,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,OAAO;AAAA;AAAA,EAET,aAAa;AAAA,IACX,gBAAgB;AAAA,IAChB,OAAO;AAAA;AAAA,EAET,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,SAAS;AAAA,EAET,UAAU;AAAA,EACV,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA;AAGX,iBAAiB,UAAU,iBAAiB;AAC5C,iBAAiB,UAAU,QAAQ;AACnC,iBAAiB,UAAU,iBAAiB;AAC5C,iBAAiB,UAAU,YAAY;AACvC,iBAAiB,UAAU,OAAO;AAClC,iBAAiB,UAAU,YAAY;AACvC,iBAAiB,UAAU,iBAAiB,iBAAiB,UAAU;AACvE,iBAAiB,UAAU,kBAAkB,iBAAiB,UAAU;AAExE,OAAO,UAAU;", "names": []}