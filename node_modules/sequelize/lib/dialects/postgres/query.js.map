{"version": 3, "sources": ["../../../src/dialects/postgres/query.js"], "sourcesContent": ["'use strict';\n\nconst AbstractQuery = require('../abstract/query');\nconst QueryTypes = require('../../query-types');\nconst sequelizeErrors = require('../../errors');\nconst _ = require('lodash');\nconst { logger } = require('../../utils/logger');\n\nconst debug = logger.debugContext('sql:pg');\n\n\nclass Query extends AbstractQuery {\n  /**\n   * Rewrite query with parameters.\n   *\n   * @param {string} sql\n   * @param {Array|object} values\n   * @param {string} dialect\n   * @private\n   */\n  static formatBindParameters(sql, values, dialect) {\n    const stringReplaceFunc = value => typeof value === 'string' ? value.replace(/\\0/g, '\\\\0') : value;\n\n    let bindParam;\n    if (Array.isArray(values)) {\n      bindParam = values.map(stringReplaceFunc);\n      sql = AbstractQuery.formatBindParameters(sql, values, dialect, { skipValueReplace: true })[0];\n    } else {\n      bindParam = [];\n      let i = 0;\n      const seen = {};\n      const replacementFunc = (match, key, values) => {\n        if (seen[key] !== undefined) {\n          return seen[key];\n        }\n        if (values[key] !== undefined) {\n          i = i + 1;\n          bindParam.push(stringReplaceFunc(values[key]));\n          seen[key] = `$${i}`;\n          return `$${i}`;\n        }\n        return undefined;\n      };\n      sql = AbstractQuery.formatBindParameters(sql, values, dialect, replacementFunc)[0];\n    }\n    return [sql, bindParam];\n  }\n\n  async run(sql, parameters) {\n    const { connection } = this;\n\n    if (!_.isEmpty(this.options.searchPath)) {\n      sql = this.sequelize.getQueryInterface().queryGenerator.setSearchPath(this.options.searchPath) + sql;\n    }\n\n    if (this.sequelize.options.minifyAliases && this.options.includeAliases) {\n      _.toPairs(this.options.includeAliases)\n        // Sorting to replace the longest aliases first to prevent alias collision\n        .sort((a, b) => b[1].length - a[1].length)\n        .forEach(([alias, original]) => {\n          const reg = new RegExp(_.escapeRegExp(original), 'g');\n\n          sql = sql.replace(reg, alias);\n        });\n    }\n\n    this.sql = sql;\n\n    const query = parameters && parameters.length\n      ? new Promise((resolve, reject) => connection.query(sql, parameters, (error, result) => error ? reject(error) : resolve(result)))\n      : new Promise((resolve, reject) => connection.query(sql, (error, result) => error ? reject(error) : resolve(result)));\n\n    const complete = this._logQuery(sql, debug, parameters);\n\n    let queryResult;\n    const errForStack = new Error();\n\n    try {\n      queryResult = await query;\n    } catch (error) {\n      // set the client so that it will be reaped if the connection resets while executing\n      if (error.code === 'ECONNRESET'\n        // https://github.com/sequelize/sequelize/pull/14090\n        // pg-native throws custom exception or libpq formatted errors\n        || /Unable to set non-blocking to true/i.test(error)\n        || /SSL SYSCALL error: EOF detected/i.test(error)\n        || /Local: Authentication failure/i.test(error)\n        // https://github.com/sequelize/sequelize/pull/15144\n        || error.message === 'Query read timeout'\n      ) {\n        connection._invalid = true;\n      }\n\n      error.sql = sql;\n      error.parameters = parameters;\n      throw this.formatError(error, errForStack.stack);\n    }\n\n    complete();\n\n    let rows = Array.isArray(queryResult)\n      ? queryResult.reduce((allRows, r) => allRows.concat(r.rows || []), [])\n      : queryResult.rows;\n    const rowCount = Array.isArray(queryResult)\n      ? queryResult.reduce(\n        (count, r) => Number.isFinite(r.rowCount) ? count + r.rowCount : count,\n        0\n      )\n      : queryResult.rowCount || 0;\n\n    if (this.sequelize.options.minifyAliases && this.options.aliasesMapping) {\n      rows = rows\n        .map(row => _.toPairs(row)\n          .reduce((acc, [key, value]) => {\n            const mapping = this.options.aliasesMapping.get(key);\n            acc[mapping || key] = value;\n            return acc;\n          }, {})\n        );\n    }\n\n    const isTableNameQuery = sql.startsWith('SELECT table_name FROM information_schema.tables');\n    const isRelNameQuery = sql.startsWith('SELECT relname FROM pg_class WHERE oid IN');\n\n    if (isRelNameQuery) {\n      return rows.map(row => ({\n        name: row.relname,\n        tableName: row.relname.split('_')[0]\n      }));\n    }\n    if (isTableNameQuery) {\n      return rows.map(row => Object.values(row));\n    }\n\n    if (rows[0] && rows[0].sequelize_caught_exception !== undefined) {\n      if (rows[0].sequelize_caught_exception !== null) {\n        throw this.formatError({\n          sql,\n          parameters,\n          code: '23505',\n          detail: rows[0].sequelize_caught_exception\n        });\n      }\n      for (const row of rows) {\n        delete row.sequelize_caught_exception;\n      }\n    }\n\n    if (this.isShowIndexesQuery()) {\n      for (const row of rows) {\n        const attributes = /ON .*? (?:USING .*?\\s)?\\(([^]*)\\)/gi.exec(row.definition)[1].split(',');\n\n        // Map column index in table to column name\n        const columns = _.zipObject(\n          row.column_indexes,\n          this.sequelize.getQueryInterface().queryGenerator.fromArray(row.column_names)\n        );\n        delete row.column_indexes;\n        delete row.column_names;\n\n        let field;\n        let attribute;\n\n        // Indkey is the order of attributes in the index, specified by a string of attribute indexes\n        row.fields = row.indkey.split(' ').map((indKey, index) => {\n          field = columns[indKey];\n          // for functional indices indKey = 0\n          if (!field) {\n            return null;\n          }\n          attribute = attributes[index];\n          return {\n            attribute: field,\n            collate: attribute.match(/COLLATE \"(.*?)\"/) ? /COLLATE \"(.*?)\"/.exec(attribute)[1] : undefined,\n            order: attribute.includes('DESC') ? 'DESC' : attribute.includes('ASC') ? 'ASC' : undefined,\n            length: undefined\n          };\n        }).filter(n => n !== null);\n        delete row.columns;\n      }\n      return rows;\n    }\n    if (this.isForeignKeysQuery()) {\n      const result = [];\n      for (const row of rows) {\n        let defParts;\n        if (row.condef !== undefined && (defParts = row.condef.match(/FOREIGN KEY \\((.+)\\) REFERENCES (.+)\\((.+)\\)( ON (UPDATE|DELETE) (CASCADE|RESTRICT))?( ON (UPDATE|DELETE) (CASCADE|RESTRICT))?/))) {\n          row.id = row.constraint_name;\n          row.table = defParts[2];\n          row.from = defParts[1];\n          row.to = defParts[3];\n          let i;\n          for (i = 5; i <= 8; i += 3) {\n            if (/(UPDATE|DELETE)/.test(defParts[i])) {\n              row[`on_${defParts[i].toLowerCase()}`] = defParts[i + 1];\n            }\n          }\n        }\n        result.push(row);\n      }\n      return result;\n    }\n    if (this.isSelectQuery()) {\n      let result = rows;\n      // Postgres will treat tables as case-insensitive, so fix the case\n      // of the returned values to match attributes\n      if (this.options.raw === false && this.sequelize.options.quoteIdentifiers === false) {\n        const attrsMap = _.reduce(this.model.rawAttributes, (m, v, k) => {\n          m[k.toLowerCase()] = k;\n          return m;\n        }, {});\n        result = rows.map(row => {\n          return _.mapKeys(row, (value, key) => {\n            const targetAttr = attrsMap[key];\n            if (typeof targetAttr === 'string' && targetAttr !== key) {\n              return targetAttr;\n            }\n            return key;\n          });\n        });\n      }\n      return this.handleSelectQuery(result);\n    }\n    if (QueryTypes.DESCRIBE === this.options.type) {\n      const result = {};\n\n      for (const row of rows) {\n        result[row.Field] = {\n          type: row.Type.toUpperCase(),\n          allowNull: row.Null === 'YES',\n          defaultValue: row.Default,\n          comment: row.Comment,\n          special: row.special ? this.sequelize.getQueryInterface().queryGenerator.fromArray(row.special) : [],\n          primaryKey: row.Constraint === 'PRIMARY KEY'\n        };\n\n        if (result[row.Field].type === 'BOOLEAN') {\n          result[row.Field].defaultValue = { 'false': false, 'true': true }[result[row.Field].defaultValue];\n\n          if (result[row.Field].defaultValue === undefined) {\n            result[row.Field].defaultValue = null;\n          }\n        }\n\n        if (typeof result[row.Field].defaultValue === 'string') {\n          result[row.Field].defaultValue = result[row.Field].defaultValue.replace(/'/g, '');\n\n          if (result[row.Field].defaultValue.includes('::')) {\n            const split = result[row.Field].defaultValue.split('::');\n            if (split[1].toLowerCase() !== 'regclass)') {\n              result[row.Field].defaultValue = split[0];\n            }\n          }\n        }\n      }\n\n      return result;\n    }\n    if (this.isVersionQuery()) {\n      return rows[0].server_version;\n    }\n    if (this.isShowOrDescribeQuery()) {\n      return rows;\n    }\n    if (QueryTypes.BULKUPDATE === this.options.type) {\n      if (!this.options.returning) {\n        return parseInt(rowCount, 10);\n      }\n      return this.handleSelectQuery(rows);\n    }\n    if (QueryTypes.BULKDELETE === this.options.type) {\n      return parseInt(rowCount, 10);\n    }\n    if (this.isInsertQuery() || this.isUpdateQuery() || this.isUpsertQuery()) {\n      if (this.instance && this.instance.dataValues) {\n        // If we are creating an instance, and we get no rows, the create failed but did not throw.\n        // This probably means a conflict happened and was ignored, to avoid breaking a transaction.\n        if (this.isInsertQuery() && !this.isUpsertQuery() && rowCount === 0) {\n          throw new sequelizeErrors.EmptyResultError();\n        }\n\n        for (const key in rows[0]) {\n          if (Object.prototype.hasOwnProperty.call(rows[0], key)) {\n            const record = rows[0][key];\n\n            const attr = _.find(this.model.rawAttributes, attribute => attribute.fieldName === key || attribute.field === key);\n\n            this.instance.dataValues[attr && attr.fieldName || key] = record;\n          }\n        }\n      }\n\n      if (this.isUpsertQuery()) {\n        return [\n          this.instance,\n          null\n        ];\n      }\n\n      return [\n        this.instance || rows && (this.options.plain && rows[0] || rows) || undefined,\n        rowCount\n      ];\n    }\n    if (this.isRawQuery()) {\n      return [rows, queryResult];\n    }\n    return rows;\n  }\n\n  formatError(err, errStack) {\n    let match;\n    let table;\n    let index;\n    let fields;\n    let errors;\n    let message;\n\n    const code = err.code || err.sqlState;\n    const errMessage = err.message || err.messagePrimary;\n    const errDetail = err.detail || err.messageDetail;\n\n    switch (code) {\n      case '23503':\n        index = errMessage.match(/violates foreign key constraint \"(.+?)\"/);\n        index = index ? index[1] : undefined;\n        table = errMessage.match(/on table \"(.+?)\"/);\n        table = table ? table[1] : undefined;\n\n        return new sequelizeErrors.ForeignKeyConstraintError({\n          message: errMessage,\n          fields: null,\n          index,\n          table,\n          parent: err,\n          stack: errStack\n        });\n      case '23505':\n        // there are multiple different formats of error messages for this error code\n        // this regex should check at least two\n        if (errDetail && (match = errDetail.replace(/\"/g, '').match(/Key \\((.*?)\\)=\\((.*?)\\)/))) {\n          fields = _.zipObject(match[1].split(', '), match[2].split(', '));\n          errors = [];\n          message = 'Validation error';\n\n          _.forOwn(fields, (value, field) => {\n            errors.push(new sequelizeErrors.ValidationErrorItem(\n              this.getUniqueConstraintErrorMessage(field),\n              'unique violation', // sequelizeErrors.ValidationErrorItem.Origins.DB,\n              field,\n              value,\n              this.instance,\n              'not_unique'\n            ));\n          });\n\n          if (this.model && this.model.uniqueKeys) {\n            _.forOwn(this.model.uniqueKeys, constraint => {\n              if (_.isEqual(constraint.fields, Object.keys(fields)) && !!constraint.msg) {\n                message = constraint.msg;\n                return false;\n              }\n            });\n          }\n\n          return new sequelizeErrors.UniqueConstraintError({ message, errors, parent: err, fields, stack: errStack });\n        }\n\n        return new sequelizeErrors.UniqueConstraintError({\n          message: errMessage,\n          parent: err,\n          stack: errStack\n        });\n\n      case '23P01':\n        match = errDetail.match(/Key \\((.*?)\\)=\\((.*?)\\)/);\n\n        if (match) {\n          fields = _.zipObject(match[1].split(', '), match[2].split(', '));\n        }\n        message = 'Exclusion constraint error';\n\n        return new sequelizeErrors.ExclusionConstraintError({\n          message,\n          constraint: err.constraint,\n          fields,\n          table: err.table,\n          parent: err,\n          stack: errStack\n        });\n\n      case '42704':\n        if (err.sql && /(CONSTRAINT|INDEX)/gi.test(err.sql)) {\n          message = 'Unknown constraint error';\n          index = errMessage.match(/(?:constraint|index) \"(.+?)\"/i);\n          index = index ? index[1] : undefined;\n          table = errMessage.match(/relation \"(.+?)\"/i);\n          table = table ? table[1] : undefined;\n\n          throw new sequelizeErrors.UnknownConstraintError({\n            message,\n            constraint: index,\n            fields,\n            table,\n            parent: err,\n            stack: errStack\n          });\n        }\n      // falls through\n      default:\n        return new sequelizeErrors.DatabaseError(err, { stack: errStack });\n    }\n  }\n\n  isForeignKeysQuery() {\n    return /SELECT conname as constraint_name, pg_catalog\\.pg_get_constraintdef\\(r\\.oid, true\\) as condef FROM pg_catalog\\.pg_constraint r WHERE r\\.conrelid = \\(SELECT oid FROM pg_class WHERE relname = '.*' LIMIT 1\\) AND r\\.contype = 'f' ORDER BY 1;/.test(this.sql);\n  }\n\n  getInsertIdField() {\n    return 'id';\n  }\n}\n\nmodule.exports = Query;\nmodule.exports.Query = Query;\nmodule.exports.default = Query;\n"], "mappings": ";AAEA,MAAM,gBAAgB,QAAQ;AAC9B,MAAM,aAAa,QAAQ;AAC3B,MAAM,kBAAkB,QAAQ;AAChC,MAAM,IAAI,QAAQ;AAClB,MAAM,EAAE,WAAW,QAAQ;AAE3B,MAAM,QAAQ,OAAO,aAAa;AAGlC,oBAAoB,cAAc;AAAA,SASzB,qBAAqB,KAAK,QAAQ,SAAS;AAChD,UAAM,oBAAoB,WAAS,OAAO,UAAU,WAAW,MAAM,QAAQ,OAAO,SAAS;AAE7F,QAAI;AACJ,QAAI,MAAM,QAAQ,SAAS;AACzB,kBAAY,OAAO,IAAI;AACvB,YAAM,cAAc,qBAAqB,KAAK,QAAQ,SAAS,EAAE,kBAAkB,QAAQ;AAAA,WACtF;AACL,kBAAY;AACZ,UAAI,IAAI;AACR,YAAM,OAAO;AACb,YAAM,kBAAkB,CAAC,OAAO,KAAK,YAAW;AAC9C,YAAI,KAAK,SAAS,QAAW;AAC3B,iBAAO,KAAK;AAAA;AAEd,YAAI,QAAO,SAAS,QAAW;AAC7B,cAAI,IAAI;AACR,oBAAU,KAAK,kBAAkB,QAAO;AACxC,eAAK,OAAO,IAAI;AAChB,iBAAO,IAAI;AAAA;AAEb,eAAO;AAAA;AAET,YAAM,cAAc,qBAAqB,KAAK,QAAQ,SAAS,iBAAiB;AAAA;AAElF,WAAO,CAAC,KAAK;AAAA;AAAA,QAGT,IAAI,KAAK,YAAY;AACzB,UAAM,EAAE,eAAe;AAEvB,QAAI,CAAC,EAAE,QAAQ,KAAK,QAAQ,aAAa;AACvC,YAAM,KAAK,UAAU,oBAAoB,eAAe,cAAc,KAAK,QAAQ,cAAc;AAAA;AAGnG,QAAI,KAAK,UAAU,QAAQ,iBAAiB,KAAK,QAAQ,gBAAgB;AACvE,QAAE,QAAQ,KAAK,QAAQ,gBAEpB,KAAK,CAAC,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,GAAG,QAClC,QAAQ,CAAC,CAAC,OAAO,cAAc;AAC9B,cAAM,MAAM,IAAI,OAAO,EAAE,aAAa,WAAW;AAEjD,cAAM,IAAI,QAAQ,KAAK;AAAA;AAAA;AAI7B,SAAK,MAAM;AAEX,UAAM,QAAQ,cAAc,WAAW,SACnC,IAAI,QAAQ,CAAC,SAAS,WAAW,WAAW,MAAM,KAAK,YAAY,CAAC,OAAO,WAAW,QAAQ,OAAO,SAAS,QAAQ,YACtH,IAAI,QAAQ,CAAC,SAAS,WAAW,WAAW,MAAM,KAAK,CAAC,OAAO,WAAW,QAAQ,OAAO,SAAS,QAAQ;AAE9G,UAAM,WAAW,KAAK,UAAU,KAAK,OAAO;AAE5C,QAAI;AACJ,UAAM,cAAc,IAAI;AAExB,QAAI;AACF,oBAAc,MAAM;AAAA,aACb,OAAP;AAEA,UAAI,MAAM,SAAS,gBAGd,sCAAsC,KAAK,UAC3C,mCAAmC,KAAK,UACxC,iCAAiC,KAAK,UAEtC,MAAM,YAAY,sBACrB;AACA,mBAAW,WAAW;AAAA;AAGxB,YAAM,MAAM;AACZ,YAAM,aAAa;AACnB,YAAM,KAAK,YAAY,OAAO,YAAY;AAAA;AAG5C;AAEA,QAAI,OAAO,MAAM,QAAQ,eACrB,YAAY,OAAO,CAAC,SAAS,MAAM,QAAQ,OAAO,EAAE,QAAQ,KAAK,MACjE,YAAY;AAChB,UAAM,WAAW,MAAM,QAAQ,eAC3B,YAAY,OACZ,CAAC,OAAO,MAAM,OAAO,SAAS,EAAE,YAAY,QAAQ,EAAE,WAAW,OACjE,KAEA,YAAY,YAAY;AAE5B,QAAI,KAAK,UAAU,QAAQ,iBAAiB,KAAK,QAAQ,gBAAgB;AACvE,aAAO,KACJ,IAAI,SAAO,EAAE,QAAQ,KACnB,OAAO,CAAC,KAAK,CAAC,KAAK,WAAW;AAC7B,cAAM,UAAU,KAAK,QAAQ,eAAe,IAAI;AAChD,YAAI,WAAW,OAAO;AACtB,eAAO;AAAA,SACN;AAAA;AAIT,UAAM,mBAAmB,IAAI,WAAW;AACxC,UAAM,iBAAiB,IAAI,WAAW;AAEtC,QAAI,gBAAgB;AAClB,aAAO,KAAK,IAAI,SAAQ;AAAA,QACtB,MAAM,IAAI;AAAA,QACV,WAAW,IAAI,QAAQ,MAAM,KAAK;AAAA;AAAA;AAGtC,QAAI,kBAAkB;AACpB,aAAO,KAAK,IAAI,SAAO,OAAO,OAAO;AAAA;AAGvC,QAAI,KAAK,MAAM,KAAK,GAAG,+BAA+B,QAAW;AAC/D,UAAI,KAAK,GAAG,+BAA+B,MAAM;AAC/C,cAAM,KAAK,YAAY;AAAA,UACrB;AAAA,UACA;AAAA,UACA,MAAM;AAAA,UACN,QAAQ,KAAK,GAAG;AAAA;AAAA;AAGpB,iBAAW,OAAO,MAAM;AACtB,eAAO,IAAI;AAAA;AAAA;AAIf,QAAI,KAAK,sBAAsB;AAC7B,iBAAW,OAAO,MAAM;AACtB,cAAM,aAAa,sCAAsC,KAAK,IAAI,YAAY,GAAG,MAAM;AAGvF,cAAM,UAAU,EAAE,UAChB,IAAI,gBACJ,KAAK,UAAU,oBAAoB,eAAe,UAAU,IAAI;AAElE,eAAO,IAAI;AACX,eAAO,IAAI;AAEX,YAAI;AACJ,YAAI;AAGJ,YAAI,SAAS,IAAI,OAAO,MAAM,KAAK,IAAI,CAAC,QAAQ,UAAU;AACxD,kBAAQ,QAAQ;AAEhB,cAAI,CAAC,OAAO;AACV,mBAAO;AAAA;AAET,sBAAY,WAAW;AACvB,iBAAO;AAAA,YACL,WAAW;AAAA,YACX,SAAS,UAAU,MAAM,qBAAqB,kBAAkB,KAAK,WAAW,KAAK;AAAA,YACrF,OAAO,UAAU,SAAS,UAAU,SAAS,UAAU,SAAS,SAAS,QAAQ;AAAA,YACjF,QAAQ;AAAA;AAAA,WAET,OAAO,OAAK,MAAM;AACrB,eAAO,IAAI;AAAA;AAEb,aAAO;AAAA;AAET,QAAI,KAAK,sBAAsB;AAC7B,YAAM,SAAS;AACf,iBAAW,OAAO,MAAM;AACtB,YAAI;AACJ,YAAI,IAAI,WAAW,UAAc,YAAW,IAAI,OAAO,MAAM,oIAAoI;AAC/L,cAAI,KAAK,IAAI;AACb,cAAI,QAAQ,SAAS;AACrB,cAAI,OAAO,SAAS;AACpB,cAAI,KAAK,SAAS;AAClB,cAAI;AACJ,eAAK,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG;AAC1B,gBAAI,kBAAkB,KAAK,SAAS,KAAK;AACvC,kBAAI,MAAM,SAAS,GAAG,mBAAmB,SAAS,IAAI;AAAA;AAAA;AAAA;AAI5D,eAAO,KAAK;AAAA;AAEd,aAAO;AAAA;AAET,QAAI,KAAK,iBAAiB;AACxB,UAAI,SAAS;AAGb,UAAI,KAAK,QAAQ,QAAQ,SAAS,KAAK,UAAU,QAAQ,qBAAqB,OAAO;AACnF,cAAM,WAAW,EAAE,OAAO,KAAK,MAAM,eAAe,CAAC,GAAG,GAAG,MAAM;AAC/D,YAAE,EAAE,iBAAiB;AACrB,iBAAO;AAAA,WACN;AACH,iBAAS,KAAK,IAAI,SAAO;AACvB,iBAAO,EAAE,QAAQ,KAAK,CAAC,OAAO,QAAQ;AACpC,kBAAM,aAAa,SAAS;AAC5B,gBAAI,OAAO,eAAe,YAAY,eAAe,KAAK;AACxD,qBAAO;AAAA;AAET,mBAAO;AAAA;AAAA;AAAA;AAIb,aAAO,KAAK,kBAAkB;AAAA;AAEhC,QAAI,WAAW,aAAa,KAAK,QAAQ,MAAM;AAC7C,YAAM,SAAS;AAEf,iBAAW,OAAO,MAAM;AACtB,eAAO,IAAI,SAAS;AAAA,UAClB,MAAM,IAAI,KAAK;AAAA,UACf,WAAW,IAAI,SAAS;AAAA,UACxB,cAAc,IAAI;AAAA,UAClB,SAAS,IAAI;AAAA,UACb,SAAS,IAAI,UAAU,KAAK,UAAU,oBAAoB,eAAe,UAAU,IAAI,WAAW;AAAA,UAClG,YAAY,IAAI,eAAe;AAAA;AAGjC,YAAI,OAAO,IAAI,OAAO,SAAS,WAAW;AACxC,iBAAO,IAAI,OAAO,eAAe,EAAE,SAAS,OAAO,QAAQ,OAAO,OAAO,IAAI,OAAO;AAEpF,cAAI,OAAO,IAAI,OAAO,iBAAiB,QAAW;AAChD,mBAAO,IAAI,OAAO,eAAe;AAAA;AAAA;AAIrC,YAAI,OAAO,OAAO,IAAI,OAAO,iBAAiB,UAAU;AACtD,iBAAO,IAAI,OAAO,eAAe,OAAO,IAAI,OAAO,aAAa,QAAQ,MAAM;AAE9E,cAAI,OAAO,IAAI,OAAO,aAAa,SAAS,OAAO;AACjD,kBAAM,QAAQ,OAAO,IAAI,OAAO,aAAa,MAAM;AACnD,gBAAI,MAAM,GAAG,kBAAkB,aAAa;AAC1C,qBAAO,IAAI,OAAO,eAAe,MAAM;AAAA;AAAA;AAAA;AAAA;AAM/C,aAAO;AAAA;AAET,QAAI,KAAK,kBAAkB;AACzB,aAAO,KAAK,GAAG;AAAA;AAEjB,QAAI,KAAK,yBAAyB;AAChC,aAAO;AAAA;AAET,QAAI,WAAW,eAAe,KAAK,QAAQ,MAAM;AAC/C,UAAI,CAAC,KAAK,QAAQ,WAAW;AAC3B,eAAO,SAAS,UAAU;AAAA;AAE5B,aAAO,KAAK,kBAAkB;AAAA;AAEhC,QAAI,WAAW,eAAe,KAAK,QAAQ,MAAM;AAC/C,aAAO,SAAS,UAAU;AAAA;AAE5B,QAAI,KAAK,mBAAmB,KAAK,mBAAmB,KAAK,iBAAiB;AACxE,UAAI,KAAK,YAAY,KAAK,SAAS,YAAY;AAG7C,YAAI,KAAK,mBAAmB,CAAC,KAAK,mBAAmB,aAAa,GAAG;AACnE,gBAAM,IAAI,gBAAgB;AAAA;AAG5B,mBAAW,OAAO,KAAK,IAAI;AACzB,cAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,MAAM;AACtD,kBAAM,SAAS,KAAK,GAAG;AAEvB,kBAAM,OAAO,EAAE,KAAK,KAAK,MAAM,eAAe,eAAa,UAAU,cAAc,OAAO,UAAU,UAAU;AAE9G,iBAAK,SAAS,WAAW,QAAQ,KAAK,aAAa,OAAO;AAAA;AAAA;AAAA;AAKhE,UAAI,KAAK,iBAAiB;AACxB,eAAO;AAAA,UACL,KAAK;AAAA,UACL;AAAA;AAAA;AAIJ,aAAO;AAAA,QACL,KAAK,YAAY,QAAS,MAAK,QAAQ,SAAS,KAAK,MAAM,SAAS;AAAA,QACpE;AAAA;AAAA;AAGJ,QAAI,KAAK,cAAc;AACrB,aAAO,CAAC,MAAM;AAAA;AAEhB,WAAO;AAAA;AAAA,EAGT,YAAY,KAAK,UAAU;AACzB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ,UAAM,OAAO,IAAI,QAAQ,IAAI;AAC7B,UAAM,aAAa,IAAI,WAAW,IAAI;AACtC,UAAM,YAAY,IAAI,UAAU,IAAI;AAEpC,YAAQ;AAAA,WACD;AACH,gBAAQ,WAAW,MAAM;AACzB,gBAAQ,QAAQ,MAAM,KAAK;AAC3B,gBAAQ,WAAW,MAAM;AACzB,gBAAQ,QAAQ,MAAM,KAAK;AAE3B,eAAO,IAAI,gBAAgB,0BAA0B;AAAA,UACnD,SAAS;AAAA,UACT,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,UACR,OAAO;AAAA;AAAA,WAEN;AAGH,YAAI,aAAc,SAAQ,UAAU,QAAQ,MAAM,IAAI,MAAM,6BAA6B;AACvF,mBAAS,EAAE,UAAU,MAAM,GAAG,MAAM,OAAO,MAAM,GAAG,MAAM;AAC1D,mBAAS;AACT,oBAAU;AAEV,YAAE,OAAO,QAAQ,CAAC,OAAO,UAAU;AACjC,mBAAO,KAAK,IAAI,gBAAgB,oBAC9B,KAAK,gCAAgC,QACrC,oBACA,OACA,OACA,KAAK,UACL;AAAA;AAIJ,cAAI,KAAK,SAAS,KAAK,MAAM,YAAY;AACvC,cAAE,OAAO,KAAK,MAAM,YAAY,gBAAc;AAC5C,kBAAI,EAAE,QAAQ,WAAW,QAAQ,OAAO,KAAK,YAAY,CAAC,CAAC,WAAW,KAAK;AACzE,0BAAU,WAAW;AACrB,uBAAO;AAAA;AAAA;AAAA;AAKb,iBAAO,IAAI,gBAAgB,sBAAsB,EAAE,SAAS,QAAQ,QAAQ,KAAK,QAAQ,OAAO;AAAA;AAGlG,eAAO,IAAI,gBAAgB,sBAAsB;AAAA,UAC/C,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA;AAAA,WAGN;AACH,gBAAQ,UAAU,MAAM;AAExB,YAAI,OAAO;AACT,mBAAS,EAAE,UAAU,MAAM,GAAG,MAAM,OAAO,MAAM,GAAG,MAAM;AAAA;AAE5D,kBAAU;AAEV,eAAO,IAAI,gBAAgB,yBAAyB;AAAA,UAClD;AAAA,UACA,YAAY,IAAI;AAAA,UAChB;AAAA,UACA,OAAO,IAAI;AAAA,UACX,QAAQ;AAAA,UACR,OAAO;AAAA;AAAA,WAGN;AACH,YAAI,IAAI,OAAO,uBAAuB,KAAK,IAAI,MAAM;AACnD,oBAAU;AACV,kBAAQ,WAAW,MAAM;AACzB,kBAAQ,QAAQ,MAAM,KAAK;AAC3B,kBAAQ,WAAW,MAAM;AACzB,kBAAQ,QAAQ,MAAM,KAAK;AAE3B,gBAAM,IAAI,gBAAgB,uBAAuB;AAAA,YAC/C;AAAA,YACA,YAAY;AAAA,YACZ;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,YACR,OAAO;AAAA;AAAA;AAAA;AAKX,eAAO,IAAI,gBAAgB,cAAc,KAAK,EAAE,OAAO;AAAA;AAAA;AAAA,EAI7D,qBAAqB;AACnB,WAAO,gPAAgP,KAAK,KAAK;AAAA;AAAA,EAGnQ,mBAAmB;AACjB,WAAO;AAAA;AAAA;AAIX,OAAO,UAAU;AACjB,OAAO,QAAQ,QAAQ;AACvB,OAAO,QAAQ,UAAU;", "names": []}