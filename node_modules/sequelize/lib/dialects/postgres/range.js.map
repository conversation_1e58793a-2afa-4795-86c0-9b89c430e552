{"version": 3, "sources": ["../../../src/dialects/postgres/range.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\n\nfunction stringifyRangeBound(bound) {\n  if (bound === null) {\n    return '' ;\n  }\n  if (bound === Infinity || bound === -Infinity) {\n    return bound.toString().toLowerCase();\n  }\n  return JSON.stringify(bound);\n}\n\nfunction parseRangeBound(bound, parseType) {\n  if (!bound) {\n    return null;\n  }\n  if (bound === 'infinity') {\n    return Infinity;\n  }\n  if (bound === '-infinity') {\n    return -Infinity;\n  }\n  return parseType(bound);\n\n}\n\nfunction stringify(data) {\n  if (data === null) return null;\n\n  if (!Array.isArray(data)) throw new Error('range must be an array');\n  if (!data.length) return 'empty';\n  if (data.length !== 2) throw new Error('range array length must be 0 (empty) or 2 (lower and upper bounds)');\n\n  if (Object.prototype.hasOwnProperty.call(data, 'inclusive')) {\n    if (data.inclusive === false) data.inclusive = [false, false];\n    else if (!data.inclusive) data.inclusive = [true, false];\n    else if (data.inclusive === true) data.inclusive = [true, true];\n  } else {\n    data.inclusive = [true, false];\n  }\n\n  _.each(data, (value, index) => {\n    if (_.isObject(value)) {\n      if (Object.prototype.hasOwnProperty.call(value, 'inclusive')) data.inclusive[index] = !!value.inclusive;\n      if (Object.prototype.hasOwnProperty.call(value, 'value')) data[index] = value.value;\n    }\n  });\n\n  const lowerBound = stringifyRangeBound(data[0]);\n  const upperBound = stringifyRangeBound(data[1]);\n\n  return `${(data.inclusive[0] ? '[' : '(') + lowerBound},${upperBound}${data.inclusive[1] ? ']' : ')'}`;\n}\nexports.stringify = stringify;\n\nfunction parse(value, parser) {\n  if (value === null) return null;\n  if (value === 'empty') {\n    return [];\n  }\n\n  let result = value\n    .substring(1, value.length - 1)\n    .split(',', 2);\n\n  if (result.length !== 2) return value;\n\n  result = result.map((item, index) => {\n    return {\n      value: parseRangeBound(item, parser),\n      inclusive: index === 0 ? value[0] === '[' : value[value.length - 1] === ']'\n    };\n  });\n\n  return result;\n}\nexports.parse = parse;\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAElB,6BAA6B,OAAO;AAClC,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA;AAET,MAAI,UAAU,YAAY,UAAU,WAAW;AAC7C,WAAO,MAAM,WAAW;AAAA;AAE1B,SAAO,KAAK,UAAU;AAAA;AAGxB,yBAAyB,OAAO,WAAW;AACzC,MAAI,CAAC,OAAO;AACV,WAAO;AAAA;AAET,MAAI,UAAU,YAAY;AACxB,WAAO;AAAA;AAET,MAAI,UAAU,aAAa;AACzB,WAAO;AAAA;AAET,SAAO,UAAU;AAAA;AAInB,mBAAmB,MAAM;AACvB,MAAI,SAAS;AAAM,WAAO;AAE1B,MAAI,CAAC,MAAM,QAAQ;AAAO,UAAM,IAAI,MAAM;AAC1C,MAAI,CAAC,KAAK;AAAQ,WAAO;AACzB,MAAI,KAAK,WAAW;AAAG,UAAM,IAAI,MAAM;AAEvC,MAAI,OAAO,UAAU,eAAe,KAAK,MAAM,cAAc;AAC3D,QAAI,KAAK,cAAc;AAAO,WAAK,YAAY,CAAC,OAAO;AAAA,aAC9C,CAAC,KAAK;AAAW,WAAK,YAAY,CAAC,MAAM;AAAA,aACzC,KAAK,cAAc;AAAM,WAAK,YAAY,CAAC,MAAM;AAAA,SACrD;AACL,SAAK,YAAY,CAAC,MAAM;AAAA;AAG1B,IAAE,KAAK,MAAM,CAAC,OAAO,UAAU;AAC7B,QAAI,EAAE,SAAS,QAAQ;AACrB,UAAI,OAAO,UAAU,eAAe,KAAK,OAAO;AAAc,aAAK,UAAU,SAAS,CAAC,CAAC,MAAM;AAC9F,UAAI,OAAO,UAAU,eAAe,KAAK,OAAO;AAAU,aAAK,SAAS,MAAM;AAAA;AAAA;AAIlF,QAAM,aAAa,oBAAoB,KAAK;AAC5C,QAAM,aAAa,oBAAoB,KAAK;AAE5C,SAAO,GAAI,MAAK,UAAU,KAAK,MAAM,OAAO,cAAc,aAAa,KAAK,UAAU,KAAK,MAAM;AAAA;AAEnG,QAAQ,YAAY;AAEpB,eAAe,OAAO,QAAQ;AAC5B,MAAI,UAAU;AAAM,WAAO;AAC3B,MAAI,UAAU,SAAS;AACrB,WAAO;AAAA;AAGT,MAAI,SAAS,MACV,UAAU,GAAG,MAAM,SAAS,GAC5B,MAAM,KAAK;AAEd,MAAI,OAAO,WAAW;AAAG,WAAO;AAEhC,WAAS,OAAO,IAAI,CAAC,MAAM,UAAU;AACnC,WAAO;AAAA,MACL,OAAO,gBAAgB,MAAM;AAAA,MAC7B,WAAW,UAAU,IAAI,MAAM,OAAO,MAAM,MAAM,MAAM,SAAS,OAAO;AAAA;AAAA;AAI5E,SAAO;AAAA;AAET,QAAQ,QAAQ;", "names": []}