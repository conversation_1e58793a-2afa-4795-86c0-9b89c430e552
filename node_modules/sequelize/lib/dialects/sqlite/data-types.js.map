{"version": 3, "sources": ["../../../src/dialects/sqlite/data-types.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = BaseTypes => {\n  const warn = BaseTypes.ABSTRACT.warn.bind(undefined, 'https://www.sqlite.org/datatype3.html');\n\n  /**\n   * Removes unsupported SQLite options, i.e., UNSIGNED and ZEROFILL, for the integer data types.\n   *\n   * @param {object} dataType The base integer data type.\n   * @private\n   */\n  function removeUnsupportedIntegerOptions(dataType) {\n    if (dataType._zerofill || dataType._unsigned) {\n      warn(`SQLite does not support '${dataType.key}' with UNSIGNED or ZEROFILL. Plain '${dataType.key}' will be used instead.`);\n      dataType._unsigned = undefined;\n      dataType._zerofill = undefined;\n    }\n  }\n\n  /**\n   * @see https://sqlite.org/datatype3.html\n   */\n\n  BaseTypes.DATE.types.sqlite = ['DATETIME'];\n  BaseTypes.STRING.types.sqlite = ['VARCHAR', 'VARCHAR BINARY'];\n  BaseTypes.CHAR.types.sqlite = ['CHAR', 'CHAR BINARY'];\n  BaseTypes.TEXT.types.sqlite = ['TEXT'];\n  BaseTypes.TINYINT.types.sqlite = ['TINYINT'];\n  BaseTypes.SMALLINT.types.sqlite = ['SMALLINT'];\n  BaseTypes.MEDIUMINT.types.sqlite = ['MEDIUMINT'];\n  BaseTypes.INTEGER.types.sqlite = ['INTEGER'];\n  BaseTypes.BIGINT.types.sqlite = ['BIGINT'];\n  BaseTypes.FLOAT.types.sqlite = ['FLOAT'];\n  BaseTypes.TIME.types.sqlite = ['TIME'];\n  BaseTypes.DATEONLY.types.sqlite = ['DATE'];\n  BaseTypes.BOOLEAN.types.sqlite = ['TINYINT'];\n  BaseTypes.BLOB.types.sqlite = ['TINYBLOB', 'BLOB', 'LONGBLOB'];\n  BaseTypes.DECIMAL.types.sqlite = ['DECIMAL'];\n  BaseTypes.UUID.types.sqlite = ['UUID'];\n  BaseTypes.ENUM.types.sqlite = false;\n  BaseTypes.REAL.types.sqlite = ['REAL'];\n  BaseTypes.DOUBLE.types.sqlite = ['DOUBLE PRECISION'];\n  BaseTypes.GEOMETRY.types.sqlite = false;\n  BaseTypes.JSON.types.sqlite = ['JSON', 'JSONB'];\n\n  class JSONTYPE extends BaseTypes.JSON {\n    static parse(data) {\n      return JSON.parse(data);\n    }\n  }\n\n  class DATE extends BaseTypes.DATE {\n    static parse(date, options) {\n      if (!date.includes('+')) {\n        // For backwards compat. Dates inserted by sequelize < 2.0dev12 will not have a timestamp set\n        return new Date(date + options.timezone);\n      }\n      return new Date(date); // We already have a timezone stored in the string\n    }\n  }\n\n  class DATEONLY extends BaseTypes.DATEONLY {\n    static parse(date) {\n      return date;\n    }\n  }\n\n  class STRING extends BaseTypes.STRING {\n    toSql() {\n      if (this._binary) {\n        return `VARCHAR BINARY(${this._length})`;\n      }\n      return super.toSql(this);\n    }\n  }\n\n  class TEXT extends BaseTypes.TEXT {\n    toSql() {\n      if (this._length) {\n        warn('SQLite does not support TEXT with options. Plain `TEXT` will be used instead.');\n        this._length = undefined;\n      }\n      return 'TEXT';\n    }\n  }\n\n  class CITEXT extends BaseTypes.CITEXT {\n    toSql() {\n      return 'TEXT COLLATE NOCASE';\n    }\n  }\n\n  class CHAR extends BaseTypes.CHAR {\n    toSql() {\n      if (this._binary) {\n        return `CHAR BINARY(${this._length})`;\n      }\n      return super.toSql();\n    }\n  }\n\n  class NUMBER extends BaseTypes.NUMBER {\n    toSql() {\n      let result = this.key;\n      if (this._unsigned) {\n        result += ' UNSIGNED';\n      }\n      if (this._zerofill) {\n        result += ' ZEROFILL';\n      }\n      if (this._length) {\n        result += `(${this._length}`;\n        if (typeof this._decimals === 'number') {\n          result += `,${this._decimals}`;\n        }\n        result += ')';\n      }\n      return result;\n    }\n  }\n\n  class TINYINT extends BaseTypes.TINYINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n\n  class SMALLINT extends BaseTypes.SMALLINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n\n  class MEDIUMINT extends BaseTypes.MEDIUMINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n\n  class INTEGER extends BaseTypes.INTEGER {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n\n  class BIGINT extends BaseTypes.BIGINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n\n  class FLOAT extends BaseTypes.FLOAT {\n  }\n\n  class DOUBLE extends BaseTypes.DOUBLE {\n  }\n\n  class REAL extends BaseTypes.REAL { }\n\n  function parseFloating(value) {\n    if (typeof value !== 'string') {\n      return value;\n    }\n    if (value === 'NaN') {\n      return NaN;\n    }\n    if (value === 'Infinity') {\n      return Infinity;\n    }\n    if (value === '-Infinity') {\n      return -Infinity;\n    }\n  }\n  for (const floating of [FLOAT, DOUBLE, REAL]) {\n    floating.parse = parseFloating;\n  }\n\n\n  for (const num of [FLOAT, DOUBLE, REAL, TINYINT, SMALLINT, MEDIUMINT, INTEGER, BIGINT]) {\n    num.prototype.toSql = NUMBER.prototype.toSql;\n  }\n\n  class ENUM extends BaseTypes.ENUM {\n    toSql() {\n      return 'TEXT';\n    }\n  }\n\n  return {\n    DATE,\n    DATEONLY,\n    STRING,\n    CHAR,\n    NUMBER,\n    FLOAT,\n    REAL,\n    'DOUBLE PRECISION': DOUBLE,\n    TINYINT,\n    SMALLINT,\n    MEDIUMINT,\n    INTEGER,\n    BIGINT,\n    TEXT,\n    ENUM,\n    JSON: JSONTYPE,\n    CITEXT\n  };\n};\n"], "mappings": ";AAEA,OAAO,UAAU,eAAa;AAC5B,QAAM,OAAO,UAAU,SAAS,KAAK,KAAK,QAAW;AAQrD,2CAAyC,UAAU;AACjD,QAAI,SAAS,aAAa,SAAS,WAAW;AAC5C,WAAK,4BAA4B,SAAS,0CAA0C,SAAS;AAC7F,eAAS,YAAY;AACrB,eAAS,YAAY;AAAA;AAAA;AAQzB,YAAU,KAAK,MAAM,SAAS,CAAC;AAC/B,YAAU,OAAO,MAAM,SAAS,CAAC,WAAW;AAC5C,YAAU,KAAK,MAAM,SAAS,CAAC,QAAQ;AACvC,YAAU,KAAK,MAAM,SAAS,CAAC;AAC/B,YAAU,QAAQ,MAAM,SAAS,CAAC;AAClC,YAAU,SAAS,MAAM,SAAS,CAAC;AACnC,YAAU,UAAU,MAAM,SAAS,CAAC;AACpC,YAAU,QAAQ,MAAM,SAAS,CAAC;AAClC,YAAU,OAAO,MAAM,SAAS,CAAC;AACjC,YAAU,MAAM,MAAM,SAAS,CAAC;AAChC,YAAU,KAAK,MAAM,SAAS,CAAC;AAC/B,YAAU,SAAS,MAAM,SAAS,CAAC;AACnC,YAAU,QAAQ,MAAM,SAAS,CAAC;AAClC,YAAU,KAAK,MAAM,SAAS,CAAC,YAAY,QAAQ;AACnD,YAAU,QAAQ,MAAM,SAAS,CAAC;AAClC,YAAU,KAAK,MAAM,SAAS,CAAC;AAC/B,YAAU,KAAK,MAAM,SAAS;AAC9B,YAAU,KAAK,MAAM,SAAS,CAAC;AAC/B,YAAU,OAAO,MAAM,SAAS,CAAC;AACjC,YAAU,SAAS,MAAM,SAAS;AAClC,YAAU,KAAK,MAAM,SAAS,CAAC,QAAQ;AAEvC,yBAAuB,UAAU,KAAK;AAAA,WAC7B,MAAM,MAAM;AACjB,aAAO,KAAK,MAAM;AAAA;AAAA;AAItB,qBAAmB,UAAU,KAAK;AAAA,WACzB,MAAM,MAAM,SAAS;AAC1B,UAAI,CAAC,KAAK,SAAS,MAAM;AAEvB,eAAO,IAAI,KAAK,OAAO,QAAQ;AAAA;AAEjC,aAAO,IAAI,KAAK;AAAA;AAAA;AAIpB,yBAAuB,UAAU,SAAS;AAAA,WACjC,MAAM,MAAM;AACjB,aAAO;AAAA;AAAA;AAIX,uBAAqB,UAAU,OAAO;AAAA,IACpC,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,eAAO,kBAAkB,KAAK;AAAA;AAEhC,aAAO,MAAM,MAAM;AAAA;AAAA;AAIvB,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,aAAK;AACL,aAAK,UAAU;AAAA;AAEjB,aAAO;AAAA;AAAA;AAIX,uBAAqB,UAAU,OAAO;AAAA,IACpC,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,eAAO,eAAe,KAAK;AAAA;AAE7B,aAAO,MAAM;AAAA;AAAA;AAIjB,uBAAqB,UAAU,OAAO;AAAA,IACpC,QAAQ;AACN,UAAI,SAAS,KAAK;AAClB,UAAI,KAAK,WAAW;AAClB,kBAAU;AAAA;AAEZ,UAAI,KAAK,WAAW;AAClB,kBAAU;AAAA;AAEZ,UAAI,KAAK,SAAS;AAChB,kBAAU,IAAI,KAAK;AACnB,YAAI,OAAO,KAAK,cAAc,UAAU;AACtC,oBAAU,IAAI,KAAK;AAAA;AAErB,kBAAU;AAAA;AAEZ,aAAO;AAAA;AAAA;AAIX,wBAAsB,UAAU,QAAQ;AAAA,IACtC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,yBAAuB,UAAU,SAAS;AAAA,IACxC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,0BAAwB,UAAU,UAAU;AAAA,IAC1C,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,wBAAsB,UAAU,QAAQ;AAAA,IACtC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,uBAAqB,UAAU,OAAO;AAAA,IACpC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,sBAAoB,UAAU,MAAM;AAAA;AAGpC,uBAAqB,UAAU,OAAO;AAAA;AAGtC,qBAAmB,UAAU,KAAK;AAAA;AAElC,yBAAuB,OAAO;AAC5B,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO;AAAA;AAET,QAAI,UAAU,OAAO;AACnB,aAAO;AAAA;AAET,QAAI,UAAU,YAAY;AACxB,aAAO;AAAA;AAET,QAAI,UAAU,aAAa;AACzB,aAAO;AAAA;AAAA;AAGX,aAAW,YAAY,CAAC,OAAO,QAAQ,OAAO;AAC5C,aAAS,QAAQ;AAAA;AAInB,aAAW,OAAO,CAAC,OAAO,QAAQ,MAAM,SAAS,UAAU,WAAW,SAAS,SAAS;AACtF,QAAI,UAAU,QAAQ,OAAO,UAAU;AAAA;AAGzC,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA;AAAA;", "names": []}