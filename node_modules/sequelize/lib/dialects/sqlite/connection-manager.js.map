{"version": 3, "sources": ["../../../src/dialects/sqlite/connection-manager.js"], "sourcesContent": ["'use strict';\n\nconst fs = require('fs');\nconst path = require('path');\nconst AbstractConnectionManager = require('../abstract/connection-manager');\nconst { logger } = require('../../utils/logger');\nconst debug = logger.debugContext('connection:sqlite');\nconst dataTypes = require('../../data-types').sqlite;\nconst sequelizeErrors = require('../../errors');\nconst parserStore = require('../parserStore')('sqlite');\nconst { promisify } = require('util');\n\nclass ConnectionManager extends AbstractConnectionManager {\n  constructor(dialect, sequelize) {\n    super(dialect, sequelize);\n\n    // We attempt to parse file location from a connection uri\n    // but we shouldn't match sequelize default host.\n    if (this.sequelize.options.host === 'localhost') {\n      delete this.sequelize.options.host;\n    }\n\n    this.connections = {};\n    this.lib = this._loadDialectModule('sqlite3');\n    this.refreshTypeParser(dataTypes);\n  }\n\n  async _onProcessExit() {\n    await Promise.all(\n      Object.getOwnPropertyNames(this.connections)\n        .map(connection => promisify(callback => this.connections[connection].close(callback))())\n    );\n    return super._onProcessExit.call(this);\n  }\n\n  // Expose this as a method so that the parsing may be updated when the user has added additional, custom types\n  _refreshTypeParser(dataType) {\n    parserStore.refresh(dataType);\n  }\n\n  _clearTypeParser() {\n    parserStore.clear();\n  }\n\n  async getConnection(options) {\n    options = options || {};\n    options.uuid = options.uuid || 'default';\n\n    if (!!this.sequelize.options.storage !== null && this.sequelize.options.storage !== undefined) {\n      // Check explicitely for the storage option to not be set since an empty string signals\n      // SQLite will create a temporary disk-based database in that case.\n      options.storage = this.sequelize.options.storage;\n    } else {\n      options.storage = this.sequelize.options.host || ':memory:';\n    }\n\n    options.inMemory = options.storage === ':memory:' ? 1 : 0;\n\n    const dialectOptions = this.sequelize.options.dialectOptions;\n    const defaultReadWriteMode = this.lib.OPEN_READWRITE | this.lib.OPEN_CREATE;\n\n    options.readWriteMode = dialectOptions && dialectOptions.mode || defaultReadWriteMode;\n\n    if (this.connections[options.inMemory || options.uuid]) {\n      return this.connections[options.inMemory || options.uuid];\n    }\n\n    if (!options.inMemory && (options.readWriteMode & this.lib.OPEN_CREATE) !== 0) {\n      // automatic path provision for `options.storage`\n      fs.mkdirSync(path.dirname(options.storage), { recursive: true });\n    }\n\n    const connection = await new Promise((resolve, reject) => {\n      this.connections[options.inMemory || options.uuid] = new this.lib.Database(\n        options.storage,\n        options.readWriteMode,\n        err => {\n          if (err) return reject(new sequelizeErrors.ConnectionError(err));\n          debug(`connection acquired ${options.uuid}`);\n          resolve(this.connections[options.inMemory || options.uuid]);\n        }\n      );\n    });\n\n    if (this.sequelize.config.password) {\n      // Make it possible to define and use password for sqlite encryption plugin like sqlcipher\n      connection.run(`PRAGMA KEY=${this.sequelize.escape(this.sequelize.config.password)}`);\n    }\n    if (this.sequelize.options.foreignKeys !== false) {\n      // Make it possible to define and use foreign key constraints unless\n      // explicitly disallowed. It's still opt-in per relation\n      connection.run('PRAGMA FOREIGN_KEYS=ON');\n    }\n\n    return connection;\n  }\n\n  releaseConnection(connection, force) {\n    if (connection.filename === ':memory:' && force !== true) return;\n\n    if (connection.uuid) {\n      connection.close();\n      debug(`connection released ${connection.uuid}`);\n      delete this.connections[connection.uuid];\n    }\n  }\n}\n\nmodule.exports = ConnectionManager;\nmodule.exports.ConnectionManager = ConnectionManager;\nmodule.exports.default = ConnectionManager;\n"], "mappings": ";AAEA,MAAM,KAAK,QAAQ;AACnB,MAAM,OAAO,QAAQ;AACrB,MAAM,4BAA4B,QAAQ;AAC1C,MAAM,EAAE,WAAW,QAAQ;AAC3B,MAAM,QAAQ,OAAO,aAAa;AAClC,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,kBAAkB,QAAQ;AAChC,MAAM,cAAc,QAAQ,kBAAkB;AAC9C,MAAM,EAAE,cAAc,QAAQ;AAE9B,gCAAgC,0BAA0B;AAAA,EACxD,YAAY,SAAS,WAAW;AAC9B,UAAM,SAAS;AAIf,QAAI,KAAK,UAAU,QAAQ,SAAS,aAAa;AAC/C,aAAO,KAAK,UAAU,QAAQ;AAAA;AAGhC,SAAK,cAAc;AACnB,SAAK,MAAM,KAAK,mBAAmB;AACnC,SAAK,kBAAkB;AAAA;AAAA,QAGnB,iBAAiB;AACrB,UAAM,QAAQ,IACZ,OAAO,oBAAoB,KAAK,aAC7B,IAAI,gBAAc,UAAU,cAAY,KAAK,YAAY,YAAY,MAAM;AAEhF,WAAO,MAAM,eAAe,KAAK;AAAA;AAAA,EAInC,mBAAmB,UAAU;AAC3B,gBAAY,QAAQ;AAAA;AAAA,EAGtB,mBAAmB;AACjB,gBAAY;AAAA;AAAA,QAGR,cAAc,SAAS;AAC3B,cAAU,WAAW;AACrB,YAAQ,OAAO,QAAQ,QAAQ;AAE/B,QAAI,CAAC,CAAC,KAAK,UAAU,QAAQ,YAAY,QAAQ,KAAK,UAAU,QAAQ,YAAY,QAAW;AAG7F,cAAQ,UAAU,KAAK,UAAU,QAAQ;AAAA,WACpC;AACL,cAAQ,UAAU,KAAK,UAAU,QAAQ,QAAQ;AAAA;AAGnD,YAAQ,WAAW,QAAQ,YAAY,aAAa,IAAI;AAExD,UAAM,iBAAiB,KAAK,UAAU,QAAQ;AAC9C,UAAM,uBAAuB,KAAK,IAAI,iBAAiB,KAAK,IAAI;AAEhE,YAAQ,gBAAgB,kBAAkB,eAAe,QAAQ;AAEjE,QAAI,KAAK,YAAY,QAAQ,YAAY,QAAQ,OAAO;AACtD,aAAO,KAAK,YAAY,QAAQ,YAAY,QAAQ;AAAA;AAGtD,QAAI,CAAC,QAAQ,YAAa,SAAQ,gBAAgB,KAAK,IAAI,iBAAiB,GAAG;AAE7E,SAAG,UAAU,KAAK,QAAQ,QAAQ,UAAU,EAAE,WAAW;AAAA;AAG3D,UAAM,aAAa,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxD,WAAK,YAAY,QAAQ,YAAY,QAAQ,QAAQ,IAAI,KAAK,IAAI,SAChE,QAAQ,SACR,QAAQ,eACR,SAAO;AACL,YAAI;AAAK,iBAAO,OAAO,IAAI,gBAAgB,gBAAgB;AAC3D,cAAM,uBAAuB,QAAQ;AACrC,gBAAQ,KAAK,YAAY,QAAQ,YAAY,QAAQ;AAAA;AAAA;AAK3D,QAAI,KAAK,UAAU,OAAO,UAAU;AAElC,iBAAW,IAAI,cAAc,KAAK,UAAU,OAAO,KAAK,UAAU,OAAO;AAAA;AAE3E,QAAI,KAAK,UAAU,QAAQ,gBAAgB,OAAO;AAGhD,iBAAW,IAAI;AAAA;AAGjB,WAAO;AAAA;AAAA,EAGT,kBAAkB,YAAY,OAAO;AACnC,QAAI,WAAW,aAAa,cAAc,UAAU;AAAM;AAE1D,QAAI,WAAW,MAAM;AACnB,iBAAW;AACX,YAAM,uBAAuB,WAAW;AACxC,aAAO,KAAK,YAAY,WAAW;AAAA;AAAA;AAAA;AAKzC,OAAO,UAAU;AACjB,OAAO,QAAQ,oBAAoB;AACnC,OAAO,QAAQ,UAAU;", "names": []}