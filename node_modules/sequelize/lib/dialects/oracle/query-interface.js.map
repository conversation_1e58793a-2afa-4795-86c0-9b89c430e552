{"version": 3, "sources": ["../../../src/dialects/oracle/query-interface.js"], "sourcesContent": ["// Copyright (c) 2022, Oracle and/or its affiliates. All rights reserved\n\n'use strict';\nconst { QueryInterface } = require('../abstract/query-interface');\nconst QueryTypes = require('../../query-types');\n\nconst _ = require('lodash');\n/**\n * The interface that <PERSON><PERSON><PERSON> uses to talk with Oracle database\n */\nexport class OracleQueryInterface extends QueryInterface {\n\n  /**\n   * Upsert\n   *\n   * @param {string} tableName    table to upsert on\n   * @param {object} insertValues values to be inserted, mapped to field name\n   * @param {object} updateValues values to be updated, mapped to field name\n   * @param {object} where        where conditions, which can be used for UPDATE part when INSERT fails\n   * @param {object} options      query options\n   *\n   * @returns {Promise<boolean,?number>} Resolves an array with <created, primaryKey>\n   */\n  async upsert(tableName, insertValues, updateValues, where, options) {\n    options = { ...options };\n\n    const model = options.model;\n    const primaryKeys = Object.values(model.primaryKeys).map(item => item.field);\n    const uniqueKeys = Object.values(model.uniqueKeys).filter(c => c.fields.length > 0).map(c => c.fields);\n    const indexKeys = Object.values(model._indexes).filter(c => c.unique && c.fields.length > 0).map(c => c.fields);\n\n    options.type = QueryTypes.UPSERT;\n    options.updateOnDuplicate = Object.keys(updateValues);\n    options.upsertKeys = [];\n\n    // For fields in updateValues, try to find a constraint or unique index\n    // that includes given field. Only first matching upsert key is used.\n    for (const field of options.updateOnDuplicate) {\n      const uniqueKey = uniqueKeys.find(fields => fields.includes(field));\n      if (uniqueKey) {\n        options.upsertKeys = uniqueKey;\n        break;\n      }\n\n      const indexKey = indexKeys.find(fields => fields.includes(field));\n      if (indexKey) {\n        options.upsertKeys = indexKey;\n        break;\n      }\n    }\n\n    // Always use PK, if no constraint available OR update data contains PK\n    if (\n      options.upsertKeys.length === 0\n      || _.intersection(options.updateOnDuplicate, primaryKeys).length\n    ) {\n      options.upsertKeys = primaryKeys;\n    }\n\n    options.upsertKeys = _.uniq(options.upsertKeys);\n\n    let whereHasNull = false;\n\n    primaryKeys.forEach(element => {\n      if (where[element] === null) {\n        whereHasNull = true;\n      }\n    });\n\n    if (whereHasNull === true) {\n      where = options.upsertKeys.reduce((result, attribute) => {\n        result[attribute] = insertValues[attribute];\n        return result;\n      }, {}); \n    }\n\n    const sql = this.queryGenerator.upsertQuery(tableName, insertValues, updateValues, where, model, options);\n    // we need set this to undefined otherwise sequelize would raise an error\n    // Error: Both `sql.bind` and `options.bind` cannot be set at the same time\n    if (sql.bind) {\n      options.bind = undefined;\n    }\n    return await this.sequelize.query(sql, options);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAGA,MAAM,EAAE,mBAAmB,QAAQ;AACnC,MAAM,aAAa,QAAQ;AAE3B,MAAM,IAAI,QAAQ;AAIX,mCAAmC,eAAe;AAAA,QAajD,OAAO,WAAW,cAAc,cAAc,OAAO,SAAS;AAClE,cAAU,mBAAK;AAEf,UAAM,QAAQ,QAAQ;AACtB,UAAM,cAAc,OAAO,OAAO,MAAM,aAAa,IAAI,UAAQ,KAAK;AACtE,UAAM,aAAa,OAAO,OAAO,MAAM,YAAY,OAAO,OAAK,EAAE,OAAO,SAAS,GAAG,IAAI,OAAK,EAAE;AAC/F,UAAM,YAAY,OAAO,OAAO,MAAM,UAAU,OAAO,OAAK,EAAE,UAAU,EAAE,OAAO,SAAS,GAAG,IAAI,OAAK,EAAE;AAExG,YAAQ,OAAO,WAAW;AAC1B,YAAQ,oBAAoB,OAAO,KAAK;AACxC,YAAQ,aAAa;AAIrB,eAAW,SAAS,QAAQ,mBAAmB;AAC7C,YAAM,YAAY,WAAW,KAAK,YAAU,OAAO,SAAS;AAC5D,UAAI,WAAW;AACb,gBAAQ,aAAa;AACrB;AAAA;AAGF,YAAM,WAAW,UAAU,KAAK,YAAU,OAAO,SAAS;AAC1D,UAAI,UAAU;AACZ,gBAAQ,aAAa;AACrB;AAAA;AAAA;AAKJ,QACE,QAAQ,WAAW,WAAW,KAC3B,EAAE,aAAa,QAAQ,mBAAmB,aAAa,QAC1D;AACA,cAAQ,aAAa;AAAA;AAGvB,YAAQ,aAAa,EAAE,KAAK,QAAQ;AAEpC,QAAI,eAAe;AAEnB,gBAAY,QAAQ,aAAW;AAC7B,UAAI,MAAM,aAAa,MAAM;AAC3B,uBAAe;AAAA;AAAA;AAInB,QAAI,iBAAiB,MAAM;AACzB,cAAQ,QAAQ,WAAW,OAAO,CAAC,QAAQ,cAAc;AACvD,eAAO,aAAa,aAAa;AACjC,eAAO;AAAA,SACN;AAAA;AAGL,UAAM,MAAM,KAAK,eAAe,YAAY,WAAW,cAAc,cAAc,OAAO,OAAO;AAGjG,QAAI,IAAI,MAAM;AACZ,cAAQ,OAAO;AAAA;AAEjB,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA;", "names": []}