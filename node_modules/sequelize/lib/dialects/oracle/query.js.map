{"version": 3, "sources": ["../../../src/dialects/oracle/query.js"], "sourcesContent": ["// Copyright (c) 2022, Oracle and/or its affiliates. All rights reserved\n\n'use strict';\n\nconst AbstractQuery = require('../abstract/query');\nconst SequelizeErrors = require('../../errors');\nconst parserStore = require('../parserStore')('oracle');\nconst _ = require('lodash');\nconst Utils = require('../../utils');\nconst { logger } = require('../../utils/logger');\n\nconst debug = logger.debugContext('sql:oracle');\n\nexport class OracleQuery extends AbstractQuery {\n  constructor(connection, sequelize, options) {\n    super(connection, sequelize, options);\n    this.options = _.extend(\n      {\n        logging: console.log,\n        plain: false,\n        raw: false\n      },\n      options || {}\n    );\n\n    this.checkLoggingOption();\n    this.outFormat = options.outFormat || this.sequelize.connectionManager.lib.OBJECT;\n  }\n\n  getInsertIdField() {\n    return 'id';\n  }\n\n  getExecOptions() {\n    const execOpts = { outFormat: this.outFormat, autoCommit: this.autoCommit };\n\n    // We set the oracledb\n    const oracledb = this.sequelize.connectionManager.lib;\n\n    if (this.model && this.isSelectQuery()) {\n      const fInfo = {};\n      const keys = Object.keys(this.model.tableAttributes);\n      for (const key of keys) {\n        const keyValue = this.model.tableAttributes[key];\n        if (keyValue.type.key === 'DECIMAL') {\n          fInfo[key] = { type: oracledb.STRING };\n        }\n        // Fetching BIGINT as string since, node-oracledb doesn't support JS BIGINT yet\n        if (keyValue.type.key === 'BIGINT') {\n          fInfo[key] = { type: oracledb.STRING };\n        }\n      }\n      if ( fInfo ) {\n        execOpts.fetchInfo = fInfo;\n      }\n    }\n    return execOpts;\n  }\n\n  /**\n   * convert binding values for unsupported\n   * types in connector library\n   *\n   * @param {string} bindingDictionary a string representing the key to scan\n   * @param {object} oracledb native oracle library\n   * @private\n   */\n  _convertBindAttributes(bindingDictionary, oracledb) {\n    if (this.model && this.options[bindingDictionary]) {\n      // check against model if we have some BIGINT\n      const keys = Object.keys(this.model.tableAttributes);\n      for (const key of keys) {\n        const keyValue = this.model.tableAttributes[key];\n        if (keyValue.type.key === 'BIGINT') {\n          const oldBinding = this.options[bindingDictionary][key];\n          if (oldBinding) {\n            this.options[bindingDictionary][key] = {\n              ...oldBinding,\n              type: oracledb.STRING,\n              maxSize: 10000000 //TOTALLY ARBITRARY Number to prevent query failure\n            };\n          }\n        }\n      }\n    }\n  }\n\n  async run(sql, parameters) {\n    // We set the oracledb\n    const oracledb = this.sequelize.connectionManager.lib;\n    const complete = this._logQuery(sql, debug, parameters);\n    const outParameters = [];\n    const bindParameters = [];\n    const bindDef = [];\n\n    if (!sql.match(/END;$/)) {\n      this.sql = sql.replace(/; *$/, '');\n    } else {\n      this.sql = sql;\n    }\n\n    // When this.options.bindAttributes exists then it is an insertQuery/upsertQuery\n    // So we insert the return bind direction and type\n    if (this.options.outBindAttributes && (Array.isArray(parameters) || _.isPlainObject(parameters))) {\n      this._convertBindAttributes('outBindAttributes', oracledb);\n      outParameters.push(...Object.values(this.options.outBindAttributes));\n      // For upsertQuery we need to push the bindDef for isUpdate\n      if (this.isUpsertQuery()) {\n        outParameters.push({ dir: oracledb.BIND_OUT });\n      }\n    }\n\n    this.bindParameters = outParameters;\n    // construct input binds from parameters for single row insert execute call\n    // ex: [3, 4,...]\n    if (Array.isArray(parameters) || _.isPlainObject(parameters)) {\n      if (this.options.executeMany) {\n        // Constructing BindDefs for ExecuteMany call\n        // Building the bindDef for in and out binds\n        this._convertBindAttributes('inbindAttributes', oracledb);\n        bindDef.push(...Object.values(this.options.inbindAttributes));\n        bindDef.push(...outParameters);\n        this.bindParameters = parameters;\n      } else if (this.isRawQuery()) {\n        this.bindParameters = parameters;\n      } else {\n        Object.values(parameters).forEach(value => {\n          bindParameters.push(value);\n        });\n        bindParameters.push(...outParameters);\n        Object.assign(this.bindParameters, bindParameters);\n      }\n    }\n\n    // TRANSACTION SUPPORT\n    if (this.sql.startsWith('BEGIN TRANSACTION')) {\n      this.autocommit = false;\n      return Promise.resolve();\n    }\n    if (this.sql.startsWith('SET AUTOCOMMIT ON')) {\n      this.autocommit = true;\n      return Promise.resolve();\n    }\n    if (this.sql.startsWith('SET AUTOCOMMIT OFF')) {\n      this.autocommit = false;\n      return Promise.resolve();\n    }\n    if (this.sql.startsWith('DECLARE x NUMBER')) {\n      // Calling a stored procedure for bulkInsert with NO attributes, returns nothing\n      if (this.autoCommit === undefined) {\n        if (this.connection.uuid) {\n          this.autoCommit = false;\n        } else {\n          this.autoCommit = true;\n        }\n      }\n\n      try {\n        await this.connection.execute(this.sql, this.bindParameters, { autoCommit: this.autoCommit });\n        return Object.create(null);\n      } catch (error) {\n        throw this.formatError(error);\n      } finally {\n        complete();\n      }\n    }\n    if (this.sql.startsWith('BEGIN')) {\n      // Call to stored procedures - BEGIN TRANSACTION has been treated before\n      if (this.autoCommit === undefined) {\n        if (this.connection.uuid) {\n          this.autoCommit = false;\n        } else {\n          this.autoCommit = true;\n        }\n      }\n\n      try {\n        const result = await this.connection.execute(this.sql, this.bindParameters, {\n          outFormat: this.outFormat,\n          autoCommit: this.autoCommit\n        });\n        if (!Array.isArray(result.outBinds)) {\n          return [result.outBinds];\n        }\n        return result.outBinds;\n      } catch (error) {\n        throw this.formatError(error);\n      } finally {\n        complete();\n      }\n    }\n    if (this.sql.startsWith('COMMIT TRANSACTION')) {\n      try {\n        await this.connection.commit();\n        return Object.create(null);\n      } catch (error) {\n        throw this.formatError(error);\n      } finally {\n        complete();\n      }\n    }\n    if (this.sql.startsWith('ROLLBACK TRANSACTION')) {\n      try {\n        await this.connection.rollback();\n        return Object.create(null);\n      } catch (error) {\n        throw this.formatError(error);\n      } finally {\n        complete();\n      }\n    }\n    if (this.sql.startsWith('SET TRANSACTION')) {\n      try {\n        await this.connection.execute(this.sql, [], { autoCommit: false });\n        return Object.create(null);\n      } catch (error) {\n        throw this.formatError(error);\n      } finally {\n        complete();\n      }\n    }\n    // QUERY SUPPORT\n    // As Oracle does everything in transaction, if autoCommit is not defined, we set it to true\n    if (this.autoCommit === undefined) {\n      if (this.connection.uuid) {\n        this.autoCommit = false;\n      } else {\n        this.autoCommit = true;\n      }\n    }\n\n    // inbind parameters added byname. merge them\n    if ('inputParameters' in this.options && this.options.inputParameters !== null) {\n      Object.assign(this.bindParameters, this.options.inputParameters);\n    }\n    const execOpts = this.getExecOptions();\n    if (this.options.executeMany && bindDef.length > 0) {\n      execOpts.bindDefs = bindDef;\n    }\n    const executePromise = this.options.executeMany ? this.connection.executeMany(this.sql, this.bindParameters, execOpts) : this.connection.execute(this.sql, this.bindParameters, execOpts);\n    try {\n      const result = await executePromise;\n      return this.formatResults(result);\n    } catch (error) {\n      throw this.formatError(error);\n    } finally {\n      complete();\n    }\n  }\n\n  /**\n * The parameters to query.run function are built here\n *\n * @param {string} sql\n * @param {Array} values\n * @param {string} dialect\n */\n  static formatBindParameters(sql, values, dialect) {\n\n    const replacementFunc = (match, key, values) => {\n      if (values[key] !== undefined) {\n        return `:${key}`;\n      }\n      return undefined;\n    };\n    sql = AbstractQuery.formatBindParameters(sql, values, dialect, replacementFunc)[0];\n\n    return [sql, values];\n  }\n\n  /**\n   * Building the attribute map by matching the column names received\n   * from DB and the one in rawAttributes\n   * to sequelize format\n   *\n   * @param {object} attrsMap\n   * @param {object} rawAttributes\n   * @private\n   */\n  _getAttributeMap(attrsMap, rawAttributes) {\n    attrsMap = Object.assign(attrsMap, _.reduce(rawAttributes, (mp, _, key) => {\n      const catalogKey = this.sequelize.queryInterface.queryGenerator.getCatalogName(key);\n      mp[catalogKey] = key;\n      return mp;\n    }, {}));\n  }\n\n  /**\n   * Process rows received from the DB.\n   * Use parse function to parse the returned value\n   * to sequelize format\n   *\n   * @param {Array} rows\n   * @private\n   */\n  _processRows(rows) {\n    let result = rows;\n    let attrsMap = {};\n\n    // When quoteIdentifiers is false we need to map the DB column names\n    // To the one in attribute list\n    if (this.sequelize.options.quoteIdentifiers === false) {\n      // Building the attribute map from this.options.attributes\n      // Needed in case of an aggregate function\n      attrsMap = _.reduce(this.options.attributes, (mp, v) => {\n        // Aggregate function is of form\n        // Fn {fn: 'min', min}, so we have the name in index one of the object\n        if (typeof v === 'object') {\n          v = v[1];\n        }\n        const catalogv = this.sequelize.queryInterface.queryGenerator.getCatalogName(v);\n        mp[catalogv] = v;\n        return mp;\n      }, {});\n\n\n      // Building the attribute map by matching the column names received\n      // from DB and the one in model.rawAttributes\n      if (this.model) {\n        this._getAttributeMap(attrsMap, this.model.rawAttributes);\n      }\n\n      // If aliasesmapping exists we update the attribute map\n      if (this.options.aliasesMapping) {\n        const obj = Object.fromEntries(this.options.aliasesMapping);\n        rows = rows\n          .map(row => _.toPairs(row)\n            .reduce((acc, [key, value]) => {\n              const mapping = Object.values(obj).find(element => {\n                const catalogElement = this.sequelize.queryInterface.queryGenerator.getCatalogName(element);\n                return catalogElement === key;\n              });\n              if (mapping)\n                acc[mapping || key] = value;\n              return acc;\n            }, {})\n          );\n      }\n\n      // Modify the keys into the format that sequelize expects\n      result = rows.map(row => {\n        return _.mapKeys(row, (value, key) => {\n          const targetAttr = attrsMap[key];\n          if (typeof targetAttr === 'string' && targetAttr !== key) {\n            return targetAttr;\n          }\n          return key;\n        });\n      });\n    }\n\n    // We parse the value received from the DB based on its datatype\n    if (this.model) {\n      result = result.map(row => {\n        return _.mapValues(row, (value, key) => {\n          if (this.model.rawAttributes[key] && this.model.rawAttributes[key].type) {\n            let typeid = this.model.rawAttributes[key].type.toLocaleString();\n            if (this.model.rawAttributes[key].type.key === 'JSON') {\n              value = JSON.parse(value);\n            }\n            // For some types, the \"name\" of the type is returned with the length, we remove it\n            // For Boolean we skip this because BOOLEAN is mapped to CHAR(1) and we dont' want to\n            // remove the (1) for BOOLEAN\n            if (typeid.indexOf('(') > -1 && this.model.rawAttributes[key].type.key !== 'BOOLEAN') {\n              typeid = typeid.substr(0, typeid.indexOf('('));\n            }\n            const parse = parserStore.get(typeid);\n            if (value !== null & !!parse) {\n              value = parse(value);\n            }\n          }\n          return value;\n        });\n      });\n    }\n\n    return result;\n  }\n\n  /**\n   * High level function that handles the results of a query execution.\n   * Example:\n   * Oracle format :\n   * { rows: //All rows\n     [ [ 'Oracle Database 11g Enterprise Edition Release ********.0 - 64bit Production' ],\n       [ 'PL/SQL Release ********.0 - Production' ],\n       [ 'CORE\\t********.0\\tProduction' ],\n       [ 'TNS for 64-bit Windows: Version ********.0 - Production' ],\n       [ 'NLSRTL Version ********.0 - Production' ] ],\n    resultSet: undefined,\n    outBinds: undefined, //Used for dbms_put.line\n    rowsAffected: undefined, //Number of rows affected\n    metaData: [ { name: 'BANNER' } ] }\n  *\n  * @param {Array} data - The result of the query execution.\n  */\n  formatResults(data) {\n    let result = this.instance;\n    if (this.isInsertQuery(data)) {\n      let insertData;\n      if (data.outBinds) {\n        const keys = Object.keys(this.options.outBindAttributes);\n        insertData = data.outBinds;\n        // For one row insert out bind array is 1D array\n        // we convert it to 2D array for uniformity\n        if (this.instance) {\n          insertData = [insertData];\n        }\n        // Mapping the bind parameter to their values\n        const res = insertData.map(row =>{\n          const obj = {};\n          row.forEach((element, index) =>{\n            obj[keys[index]] = element[0];\n          });\n          return obj;\n        });\n        insertData = res;\n        // For bulk insert this.insert is undefined\n        // we map result to res, for one row insert\n        // result needs to be this.instance\n        if (!this.instance) {\n          result = res;\n        }\n      }\n      this.handleInsertQuery(insertData);\n      return [result, data.rowsAffected];\n    }\n    if (this.isShowTablesQuery()) {\n      result = this.handleShowTablesQuery(data.rows);\n    } else if (this.isDescribeQuery()) {\n      result = {};\n      // Getting the table name on which we are doing describe query\n      const table = Object.keys(this.sequelize.models);\n      const modelAttributes = {};\n      // Get the model raw attributes\n      if (this.sequelize.models && table.length > 0) {\n        this._getAttributeMap(modelAttributes, this.sequelize.models[table[0]].rawAttributes);\n      }\n      data.rows.forEach(_result => {\n        if (_result.Default) {\n          _result.Default = _result.Default.replace(\"('\", '')\n            .replace(\"')\", '')\n            .replace(/'/g, ''); /* jshint ignore: line */\n        }\n\n        if (!(modelAttributes[_result.COLUMN_NAME] in result)) {\n          let key = modelAttributes[_result.COLUMN_NAME];\n          if (!key) {\n            key = _result.COLUMN_NAME;\n          }\n\n          result[key] = {\n            type: _result.DATA_TYPE.toUpperCase(),\n            allowNull: _result.NULLABLE === 'N' ? false : true,\n            defaultValue: undefined,\n            primaryKey: _result.CONSTRAINT_TYPE === 'P'\n          };\n        }\n      });\n    } else if (this.isShowIndexesQuery()) {\n      result = this.handleShowIndexesQuery(data.rows);\n    } else if (this.isSelectQuery()) {\n      const rows = data.rows;\n      const result = this._processRows(rows);\n      return this.handleSelectQuery(result);\n    } else if (this.isCallQuery()) {\n      result = data.rows[0];\n    } else if (this.isUpdateQuery()) {\n      result = [result, data.rowsAffected];\n    } else if (this.isBulkUpdateQuery()) {\n      result = data.rowsAffected;\n    } else if (this.isBulkDeleteQuery()) {\n      result = data.rowsAffected;\n    } else if (this.isVersionQuery()) {\n      const version = data.rows[0].VERSION_FULL;\n      if (version) {\n        const versions = version.split('.');\n        result = `${versions[0]}.${versions[1]}.${versions[2]}`;\n      } else {\n        result = '0.0.0';\n      }\n    } else if (this.isForeignKeysQuery()) {\n      result = data.rows;\n    } else if (this.isUpsertQuery()) {\n      // Upsert Query, will return nothing\n      data = data.outBinds;\n      const keys = Object.keys(this.options.outBindAttributes);\n      const obj = {};\n      for (const k in keys) {\n        obj[keys[k]] = data[k];\n      }\n      obj.isUpdate = data[data.length - 1];\n      data = obj;\n      result = [{ isNewRecord: data.isUpdate, value: data }, data.isUpdate == 0];\n    } else if (this.isShowConstraintsQuery()) {\n      result = this.handleShowConstraintsQuery(data);\n    } else if (this.isRawQuery()) {\n      // If data.rows exists then it is a select query\n      // Hence we would have two components\n      // metaData and rows and we return them\n      // as [data.rows, data.metaData]\n      // Else it is result of update/upsert/insert query\n      // and it has no rows so we return [data, data]\n      if (data && data.rows) {\n        return [data.rows, data.metaData];\n      }\n      return [data, data];\n    }\n\n    return result;\n  }\n\n  handleShowConstraintsQuery(data) {\n    // Convert snake_case keys to camelCase as its generated by stored procedure\n    return data.rows.map(result => {\n      const constraint = {};\n      for (const key in result) {\n        constraint[_.camelCase(key)] = result[key].toLowerCase();\n      }\n      return constraint;\n    });\n  }\n\n  handleShowTablesQuery(results) {\n    return results.map(resultSet => {\n      return {\n        tableName: resultSet.TABLE_NAME,\n        schema: resultSet.TABLE_SCHEMA\n      };\n    });\n  }\n\n  formatError(err) {\n    let match;\n    // ORA-00001: unique constraint (USER.XXXXXXX) violated\n    match = err.message.match(/unique constraint ([\\s\\S]*) violated/);\n    if (match && match.length > 1) {\n      match[1] = match[1].replace('(', '').replace(')', '').split('.')[1]; // As we get (SEQUELIZE.UNIQNAME), we replace to have UNIQNAME\n      const errors = [];\n      let fields = [],\n        message = 'Validation error',\n        uniqueKey = null;\n\n      if (this.model) {\n        const uniqueKeys = Object.keys(this.model.uniqueKeys);\n\n        const currKey = uniqueKeys.find(key => {\n          // We check directly AND with quotes -> \"a\"\" === a || \"a\" === \"a\"\n          return key.toUpperCase() === match[1].toUpperCase() || key.toUpperCase() === `\"${match[1].toUpperCase()}\"`;\n        });\n\n        if (currKey) {\n          uniqueKey = this.model.uniqueKeys[currKey];\n          fields = uniqueKey.fields;\n        }\n\n        if (uniqueKey && !!uniqueKey.msg) {\n          message = uniqueKey.msg;\n        }\n\n        fields.forEach(field => {\n          errors.push(\n            new SequelizeErrors.ValidationErrorItem(\n              this.getUniqueConstraintErrorMessage(field),\n              'unique violation',\n              field,\n              null\n            )\n          );\n        });\n      }\n\n      return new SequelizeErrors.UniqueConstraintError({\n        message,\n        errors,\n        err,\n        fields\n      });\n    }\n\n    // ORA-02291: integrity constraint (string.string) violated - parent key not found / ORA-02292: integrity constraint (string.string) violated - child record found\n    match = err.message.match(/ORA-02291/) || err.message.match(/ORA-02292/);\n    if (match && match.length > 0) {\n      return new SequelizeErrors.ForeignKeyConstraintError({\n        fields: null,\n        index: match[1],\n        parent: err\n      });\n    }\n\n    // ORA-02443: Cannot drop constraint  - nonexistent constraint\n    match = err.message.match(/ORA-02443/);\n    if (match && match.length > 0) {\n      return new SequelizeErrors.UnknownConstraintError(match[1]);\n    }\n\n    return new SequelizeErrors.DatabaseError(err);\n  }\n\n  isShowIndexesQuery() {\n    return this.sql.indexOf('SELECT i.index_name,i.table_name, i.column_name, u.uniqueness') > -1;\n  }\n\n  isSelectCountQuery() {\n    return this.sql.toUpperCase().indexOf('SELECT COUNT(') > -1;\n  }\n\n  handleShowIndexesQuery(data) {\n    const acc = [];\n\n    // We first treat the datas\n    data.forEach(indexRecord => {\n      // We create the object\n      if (!acc[indexRecord.INDEX_NAME]) {\n        acc[indexRecord.INDEX_NAME] = {\n          unique: indexRecord.UNIQUENESS === 'UNIQUE' ? true : false,\n          primary: indexRecord.CONSTRAINT_TYPE === 'P',\n          name: indexRecord.INDEX_NAME.toLowerCase(),\n          tableName: indexRecord.TABLE_NAME.toLowerCase(),\n          type: undefined\n        };\n        acc[indexRecord.INDEX_NAME].fields = [];\n      }\n\n      // We create the fields\n      acc[indexRecord.INDEX_NAME].fields.push({\n        attribute: indexRecord.COLUMN_NAME,\n        length: undefined,\n        order: indexRecord.DESCEND,\n        collate: undefined\n      });\n    });\n\n    const returnIndexes = [];\n    const accKeys = Object.keys(acc);\n    for (const accKey of accKeys) {\n      const columns = {};\n      columns.fields = acc[accKey].fields;\n      // We are generating index field name in the format sequelize expects\n      // to avoid creating a unique index on auto-generated index name\n      if (acc[accKey].name.match(/sys_c[0-9]*/)) {\n        acc[accKey].name = Utils.nameIndex(columns, acc[accKey].tableName).name;\n      }\n      returnIndexes.push(acc[accKey]);\n    }\n    return returnIndexes;\n  }\n\n  handleInsertQuery(results, metaData) {\n    if (this.instance && results.length > 0) {\n      if ('pkReturnVal' in results[0]) {\n        // The PK of the table is a reserved word (ex : uuid), we have to change the name in the result for the model to find the value correctly\n        results[0][this.model.primaryKeyAttribute] = results[0].pkReturnVal;\n        delete results[0].pkReturnVal;\n      }\n      // add the inserted row id to the instance\n      const autoIncrementField = this.model.autoIncrementAttribute;\n      let autoIncrementFieldAlias = null,\n        id = null;\n\n      if (\n        Object.prototype.hasOwnProperty.call(this.model.rawAttributes, autoIncrementField) &&\n        this.model.rawAttributes[autoIncrementField].field !== undefined\n      )\n        autoIncrementFieldAlias = this.model.rawAttributes[autoIncrementField].field;\n\n      id = id || results && results[0][this.getInsertIdField()];\n      id = id || metaData && metaData[this.getInsertIdField()];\n      id = id || results && results[0][autoIncrementField];\n      id = id || autoIncrementFieldAlias && results && results[0][autoIncrementFieldAlias];\n\n      this.instance[autoIncrementField] = id;\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAIA,MAAM,gBAAgB,QAAQ;AAC9B,MAAM,kBAAkB,QAAQ;AAChC,MAAM,cAAc,QAAQ,kBAAkB;AAC9C,MAAM,IAAI,QAAQ;AAClB,MAAM,QAAQ,QAAQ;AACtB,MAAM,EAAE,WAAW,QAAQ;AAE3B,MAAM,QAAQ,OAAO,aAAa;AAE3B,0BAA0B,cAAc;AAAA,EAC7C,YAAY,YAAY,WAAW,SAAS;AAC1C,UAAM,YAAY,WAAW;AAC7B,SAAK,UAAU,EAAE,OACf;AAAA,MACE,SAAS,QAAQ;AAAA,MACjB,OAAO;AAAA,MACP,KAAK;AAAA,OAEP,WAAW;AAGb,SAAK;AACL,SAAK,YAAY,QAAQ,aAAa,KAAK,UAAU,kBAAkB,IAAI;AAAA;AAAA,EAG7E,mBAAmB;AACjB,WAAO;AAAA;AAAA,EAGT,iBAAiB;AACf,UAAM,WAAW,EAAE,WAAW,KAAK,WAAW,YAAY,KAAK;AAG/D,UAAM,WAAW,KAAK,UAAU,kBAAkB;AAElD,QAAI,KAAK,SAAS,KAAK,iBAAiB;AACtC,YAAM,QAAQ;AACd,YAAM,OAAO,OAAO,KAAK,KAAK,MAAM;AACpC,iBAAW,OAAO,MAAM;AACtB,cAAM,WAAW,KAAK,MAAM,gBAAgB;AAC5C,YAAI,SAAS,KAAK,QAAQ,WAAW;AACnC,gBAAM,OAAO,EAAE,MAAM,SAAS;AAAA;AAGhC,YAAI,SAAS,KAAK,QAAQ,UAAU;AAClC,gBAAM,OAAO,EAAE,MAAM,SAAS;AAAA;AAAA;AAGlC,UAAK,OAAQ;AACX,iBAAS,YAAY;AAAA;AAAA;AAGzB,WAAO;AAAA;AAAA,EAWT,uBAAuB,mBAAmB,UAAU;AAClD,QAAI,KAAK,SAAS,KAAK,QAAQ,oBAAoB;AAEjD,YAAM,OAAO,OAAO,KAAK,KAAK,MAAM;AACpC,iBAAW,OAAO,MAAM;AACtB,cAAM,WAAW,KAAK,MAAM,gBAAgB;AAC5C,YAAI,SAAS,KAAK,QAAQ,UAAU;AAClC,gBAAM,aAAa,KAAK,QAAQ,mBAAmB;AACnD,cAAI,YAAY;AACd,iBAAK,QAAQ,mBAAmB,OAAO,iCAClC,aADkC;AAAA,cAErC,MAAM,SAAS;AAAA,cACf,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQf,IAAI,KAAK,YAAY;AAEzB,UAAM,WAAW,KAAK,UAAU,kBAAkB;AAClD,UAAM,WAAW,KAAK,UAAU,KAAK,OAAO;AAC5C,UAAM,gBAAgB;AACtB,UAAM,iBAAiB;AACvB,UAAM,UAAU;AAEhB,QAAI,CAAC,IAAI,MAAM,UAAU;AACvB,WAAK,MAAM,IAAI,QAAQ,QAAQ;AAAA,WAC1B;AACL,WAAK,MAAM;AAAA;AAKb,QAAI,KAAK,QAAQ,qBAAsB,OAAM,QAAQ,eAAe,EAAE,cAAc,cAAc;AAChG,WAAK,uBAAuB,qBAAqB;AACjD,oBAAc,KAAK,GAAG,OAAO,OAAO,KAAK,QAAQ;AAEjD,UAAI,KAAK,iBAAiB;AACxB,sBAAc,KAAK,EAAE,KAAK,SAAS;AAAA;AAAA;AAIvC,SAAK,iBAAiB;AAGtB,QAAI,MAAM,QAAQ,eAAe,EAAE,cAAc,aAAa;AAC5D,UAAI,KAAK,QAAQ,aAAa;AAG5B,aAAK,uBAAuB,oBAAoB;AAChD,gBAAQ,KAAK,GAAG,OAAO,OAAO,KAAK,QAAQ;AAC3C,gBAAQ,KAAK,GAAG;AAChB,aAAK,iBAAiB;AAAA,iBACb,KAAK,cAAc;AAC5B,aAAK,iBAAiB;AAAA,aACjB;AACL,eAAO,OAAO,YAAY,QAAQ,WAAS;AACzC,yBAAe,KAAK;AAAA;AAEtB,uBAAe,KAAK,GAAG;AACvB,eAAO,OAAO,KAAK,gBAAgB;AAAA;AAAA;AAKvC,QAAI,KAAK,IAAI,WAAW,sBAAsB;AAC5C,WAAK,aAAa;AAClB,aAAO,QAAQ;AAAA;AAEjB,QAAI,KAAK,IAAI,WAAW,sBAAsB;AAC5C,WAAK,aAAa;AAClB,aAAO,QAAQ;AAAA;AAEjB,QAAI,KAAK,IAAI,WAAW,uBAAuB;AAC7C,WAAK,aAAa;AAClB,aAAO,QAAQ;AAAA;AAEjB,QAAI,KAAK,IAAI,WAAW,qBAAqB;AAE3C,UAAI,KAAK,eAAe,QAAW;AACjC,YAAI,KAAK,WAAW,MAAM;AACxB,eAAK,aAAa;AAAA,eACb;AACL,eAAK,aAAa;AAAA;AAAA;AAItB,UAAI;AACF,cAAM,KAAK,WAAW,QAAQ,KAAK,KAAK,KAAK,gBAAgB,EAAE,YAAY,KAAK;AAChF,eAAO,OAAO,OAAO;AAAA,eACd,OAAP;AACA,cAAM,KAAK,YAAY;AAAA,gBACvB;AACA;AAAA;AAAA;AAGJ,QAAI,KAAK,IAAI,WAAW,UAAU;AAEhC,UAAI,KAAK,eAAe,QAAW;AACjC,YAAI,KAAK,WAAW,MAAM;AACxB,eAAK,aAAa;AAAA,eACb;AACL,eAAK,aAAa;AAAA;AAAA;AAItB,UAAI;AACF,cAAM,SAAS,MAAM,KAAK,WAAW,QAAQ,KAAK,KAAK,KAAK,gBAAgB;AAAA,UAC1E,WAAW,KAAK;AAAA,UAChB,YAAY,KAAK;AAAA;AAEnB,YAAI,CAAC,MAAM,QAAQ,OAAO,WAAW;AACnC,iBAAO,CAAC,OAAO;AAAA;AAEjB,eAAO,OAAO;AAAA,eACP,OAAP;AACA,cAAM,KAAK,YAAY;AAAA,gBACvB;AACA;AAAA;AAAA;AAGJ,QAAI,KAAK,IAAI,WAAW,uBAAuB;AAC7C,UAAI;AACF,cAAM,KAAK,WAAW;AACtB,eAAO,OAAO,OAAO;AAAA,eACd,OAAP;AACA,cAAM,KAAK,YAAY;AAAA,gBACvB;AACA;AAAA;AAAA;AAGJ,QAAI,KAAK,IAAI,WAAW,yBAAyB;AAC/C,UAAI;AACF,cAAM,KAAK,WAAW;AACtB,eAAO,OAAO,OAAO;AAAA,eACd,OAAP;AACA,cAAM,KAAK,YAAY;AAAA,gBACvB;AACA;AAAA;AAAA;AAGJ,QAAI,KAAK,IAAI,WAAW,oBAAoB;AAC1C,UAAI;AACF,cAAM,KAAK,WAAW,QAAQ,KAAK,KAAK,IAAI,EAAE,YAAY;AAC1D,eAAO,OAAO,OAAO;AAAA,eACd,OAAP;AACA,cAAM,KAAK,YAAY;AAAA,gBACvB;AACA;AAAA;AAAA;AAKJ,QAAI,KAAK,eAAe,QAAW;AACjC,UAAI,KAAK,WAAW,MAAM;AACxB,aAAK,aAAa;AAAA,aACb;AACL,aAAK,aAAa;AAAA;AAAA;AAKtB,QAAI,qBAAqB,KAAK,WAAW,KAAK,QAAQ,oBAAoB,MAAM;AAC9E,aAAO,OAAO,KAAK,gBAAgB,KAAK,QAAQ;AAAA;AAElD,UAAM,WAAW,KAAK;AACtB,QAAI,KAAK,QAAQ,eAAe,QAAQ,SAAS,GAAG;AAClD,eAAS,WAAW;AAAA;AAEtB,UAAM,iBAAiB,KAAK,QAAQ,cAAc,KAAK,WAAW,YAAY,KAAK,KAAK,KAAK,gBAAgB,YAAY,KAAK,WAAW,QAAQ,KAAK,KAAK,KAAK,gBAAgB;AAChL,QAAI;AACF,YAAM,SAAS,MAAM;AACrB,aAAO,KAAK,cAAc;AAAA,aACnB,OAAP;AACA,YAAM,KAAK,YAAY;AAAA,cACvB;AACA;AAAA;AAAA;AAAA,SAWG,qBAAqB,KAAK,QAAQ,SAAS;AAEhD,UAAM,kBAAkB,CAAC,OAAO,KAAK,YAAW;AAC9C,UAAI,QAAO,SAAS,QAAW;AAC7B,eAAO,IAAI;AAAA;AAEb,aAAO;AAAA;AAET,UAAM,cAAc,qBAAqB,KAAK,QAAQ,SAAS,iBAAiB;AAEhF,WAAO,CAAC,KAAK;AAAA;AAAA,EAYf,iBAAiB,UAAU,eAAe;AACxC,eAAW,OAAO,OAAO,UAAU,EAAE,OAAO,eAAe,CAAC,IAAI,IAAG,QAAQ;AACzE,YAAM,aAAa,KAAK,UAAU,eAAe,eAAe,eAAe;AAC/E,SAAG,cAAc;AACjB,aAAO;AAAA,OACN;AAAA;AAAA,EAWL,aAAa,MAAM;AACjB,QAAI,SAAS;AACb,QAAI,WAAW;AAIf,QAAI,KAAK,UAAU,QAAQ,qBAAqB,OAAO;AAGrD,iBAAW,EAAE,OAAO,KAAK,QAAQ,YAAY,CAAC,IAAI,MAAM;AAGtD,YAAI,OAAO,MAAM,UAAU;AACzB,cAAI,EAAE;AAAA;AAER,cAAM,WAAW,KAAK,UAAU,eAAe,eAAe,eAAe;AAC7E,WAAG,YAAY;AACf,eAAO;AAAA,SACN;AAKH,UAAI,KAAK,OAAO;AACd,aAAK,iBAAiB,UAAU,KAAK,MAAM;AAAA;AAI7C,UAAI,KAAK,QAAQ,gBAAgB;AAC/B,cAAM,MAAM,OAAO,YAAY,KAAK,QAAQ;AAC5C,eAAO,KACJ,IAAI,SAAO,EAAE,QAAQ,KACnB,OAAO,CAAC,KAAK,CAAC,KAAK,WAAW;AAC7B,gBAAM,UAAU,OAAO,OAAO,KAAK,KAAK,aAAW;AACjD,kBAAM,iBAAiB,KAAK,UAAU,eAAe,eAAe,eAAe;AACnF,mBAAO,mBAAmB;AAAA;AAE5B,cAAI;AACF,gBAAI,WAAW,OAAO;AACxB,iBAAO;AAAA,WACN;AAAA;AAKT,eAAS,KAAK,IAAI,SAAO;AACvB,eAAO,EAAE,QAAQ,KAAK,CAAC,OAAO,QAAQ;AACpC,gBAAM,aAAa,SAAS;AAC5B,cAAI,OAAO,eAAe,YAAY,eAAe,KAAK;AACxD,mBAAO;AAAA;AAET,iBAAO;AAAA;AAAA;AAAA;AAMb,QAAI,KAAK,OAAO;AACd,eAAS,OAAO,IAAI,SAAO;AACzB,eAAO,EAAE,UAAU,KAAK,CAAC,OAAO,QAAQ;AACtC,cAAI,KAAK,MAAM,cAAc,QAAQ,KAAK,MAAM,cAAc,KAAK,MAAM;AACvE,gBAAI,SAAS,KAAK,MAAM,cAAc,KAAK,KAAK;AAChD,gBAAI,KAAK,MAAM,cAAc,KAAK,KAAK,QAAQ,QAAQ;AACrD,sBAAQ,KAAK,MAAM;AAAA;AAKrB,gBAAI,OAAO,QAAQ,OAAO,MAAM,KAAK,MAAM,cAAc,KAAK,KAAK,QAAQ,WAAW;AACpF,uBAAS,OAAO,OAAO,GAAG,OAAO,QAAQ;AAAA;AAE3C,kBAAM,QAAQ,YAAY,IAAI;AAC9B,gBAAI,UAAU,OAAO,CAAC,CAAC,OAAO;AAC5B,sBAAQ,MAAM;AAAA;AAAA;AAGlB,iBAAO;AAAA;AAAA;AAAA;AAKb,WAAO;AAAA;AAAA,EAoBT,cAAc,MAAM;AAClB,QAAI,SAAS,KAAK;AAClB,QAAI,KAAK,cAAc,OAAO;AAC5B,UAAI;AACJ,UAAI,KAAK,UAAU;AACjB,cAAM,OAAO,OAAO,KAAK,KAAK,QAAQ;AACtC,qBAAa,KAAK;AAGlB,YAAI,KAAK,UAAU;AACjB,uBAAa,CAAC;AAAA;AAGhB,cAAM,MAAM,WAAW,IAAI,SAAM;AAC/B,gBAAM,MAAM;AACZ,cAAI,QAAQ,CAAC,SAAS,UAAS;AAC7B,gBAAI,KAAK,UAAU,QAAQ;AAAA;AAE7B,iBAAO;AAAA;AAET,qBAAa;AAIb,YAAI,CAAC,KAAK,UAAU;AAClB,mBAAS;AAAA;AAAA;AAGb,WAAK,kBAAkB;AACvB,aAAO,CAAC,QAAQ,KAAK;AAAA;AAEvB,QAAI,KAAK,qBAAqB;AAC5B,eAAS,KAAK,sBAAsB,KAAK;AAAA,eAChC,KAAK,mBAAmB;AACjC,eAAS;AAET,YAAM,QAAQ,OAAO,KAAK,KAAK,UAAU;AACzC,YAAM,kBAAkB;AAExB,UAAI,KAAK,UAAU,UAAU,MAAM,SAAS,GAAG;AAC7C,aAAK,iBAAiB,iBAAiB,KAAK,UAAU,OAAO,MAAM,IAAI;AAAA;AAEzE,WAAK,KAAK,QAAQ,aAAW;AAC3B,YAAI,QAAQ,SAAS;AACnB,kBAAQ,UAAU,QAAQ,QAAQ,QAAQ,MAAM,IAC7C,QAAQ,MAAM,IACd,QAAQ,MAAM;AAAA;AAGnB,YAAI,CAAE,iBAAgB,QAAQ,gBAAgB,SAAS;AACrD,cAAI,MAAM,gBAAgB,QAAQ;AAClC,cAAI,CAAC,KAAK;AACR,kBAAM,QAAQ;AAAA;AAGhB,iBAAO,OAAO;AAAA,YACZ,MAAM,QAAQ,UAAU;AAAA,YACxB,WAAW,QAAQ,aAAa,MAAM,QAAQ;AAAA,YAC9C,cAAc;AAAA,YACd,YAAY,QAAQ,oBAAoB;AAAA;AAAA;AAAA;AAAA,eAIrC,KAAK,sBAAsB;AACpC,eAAS,KAAK,uBAAuB,KAAK;AAAA,eACjC,KAAK,iBAAiB;AAC/B,YAAM,OAAO,KAAK;AAClB,YAAM,UAAS,KAAK,aAAa;AACjC,aAAO,KAAK,kBAAkB;AAAA,eACrB,KAAK,eAAe;AAC7B,eAAS,KAAK,KAAK;AAAA,eACV,KAAK,iBAAiB;AAC/B,eAAS,CAAC,QAAQ,KAAK;AAAA,eACd,KAAK,qBAAqB;AACnC,eAAS,KAAK;AAAA,eACL,KAAK,qBAAqB;AACnC,eAAS,KAAK;AAAA,eACL,KAAK,kBAAkB;AAChC,YAAM,UAAU,KAAK,KAAK,GAAG;AAC7B,UAAI,SAAS;AACX,cAAM,WAAW,QAAQ,MAAM;AAC/B,iBAAS,GAAG,SAAS,MAAM,SAAS,MAAM,SAAS;AAAA,aAC9C;AACL,iBAAS;AAAA;AAAA,eAEF,KAAK,sBAAsB;AACpC,eAAS,KAAK;AAAA,eACL,KAAK,iBAAiB;AAE/B,aAAO,KAAK;AACZ,YAAM,OAAO,OAAO,KAAK,KAAK,QAAQ;AACtC,YAAM,MAAM;AACZ,iBAAW,KAAK,MAAM;AACpB,YAAI,KAAK,MAAM,KAAK;AAAA;AAEtB,UAAI,WAAW,KAAK,KAAK,SAAS;AAClC,aAAO;AACP,eAAS,CAAC,EAAE,aAAa,KAAK,UAAU,OAAO,QAAQ,KAAK,YAAY;AAAA,eAC/D,KAAK,0BAA0B;AACxC,eAAS,KAAK,2BAA2B;AAAA,eAChC,KAAK,cAAc;AAO5B,UAAI,QAAQ,KAAK,MAAM;AACrB,eAAO,CAAC,KAAK,MAAM,KAAK;AAAA;AAE1B,aAAO,CAAC,MAAM;AAAA;AAGhB,WAAO;AAAA;AAAA,EAGT,2BAA2B,MAAM;AAE/B,WAAO,KAAK,KAAK,IAAI,YAAU;AAC7B,YAAM,aAAa;AACnB,iBAAW,OAAO,QAAQ;AACxB,mBAAW,EAAE,UAAU,QAAQ,OAAO,KAAK;AAAA;AAE7C,aAAO;AAAA;AAAA;AAAA,EAIX,sBAAsB,SAAS;AAC7B,WAAO,QAAQ,IAAI,eAAa;AAC9B,aAAO;AAAA,QACL,WAAW,UAAU;AAAA,QACrB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,EAKxB,YAAY,KAAK;AACf,QAAI;AAEJ,YAAQ,IAAI,QAAQ,MAAM;AAC1B,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,YAAM,KAAK,MAAM,GAAG,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,MAAM,KAAK;AACjE,YAAM,SAAS;AACf,UAAI,SAAS,IACX,UAAU,oBACV,YAAY;AAEd,UAAI,KAAK,OAAO;AACd,cAAM,aAAa,OAAO,KAAK,KAAK,MAAM;AAE1C,cAAM,UAAU,WAAW,KAAK,SAAO;AAErC,iBAAO,IAAI,kBAAkB,MAAM,GAAG,iBAAiB,IAAI,kBAAkB,IAAI,MAAM,GAAG;AAAA;AAG5F,YAAI,SAAS;AACX,sBAAY,KAAK,MAAM,WAAW;AAClC,mBAAS,UAAU;AAAA;AAGrB,YAAI,aAAa,CAAC,CAAC,UAAU,KAAK;AAChC,oBAAU,UAAU;AAAA;AAGtB,eAAO,QAAQ,WAAS;AACtB,iBAAO,KACL,IAAI,gBAAgB,oBAClB,KAAK,gCAAgC,QACrC,oBACA,OACA;AAAA;AAAA;AAMR,aAAO,IAAI,gBAAgB,sBAAsB;AAAA,QAC/C;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA;AAKJ,YAAQ,IAAI,QAAQ,MAAM,gBAAgB,IAAI,QAAQ,MAAM;AAC5D,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,aAAO,IAAI,gBAAgB,0BAA0B;AAAA,QACnD,QAAQ;AAAA,QACR,OAAO,MAAM;AAAA,QACb,QAAQ;AAAA;AAAA;AAKZ,YAAQ,IAAI,QAAQ,MAAM;AAC1B,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,aAAO,IAAI,gBAAgB,uBAAuB,MAAM;AAAA;AAG1D,WAAO,IAAI,gBAAgB,cAAc;AAAA;AAAA,EAG3C,qBAAqB;AACnB,WAAO,KAAK,IAAI,QAAQ,mEAAmE;AAAA;AAAA,EAG7F,qBAAqB;AACnB,WAAO,KAAK,IAAI,cAAc,QAAQ,mBAAmB;AAAA;AAAA,EAG3D,uBAAuB,MAAM;AAC3B,UAAM,MAAM;AAGZ,SAAK,QAAQ,iBAAe;AAE1B,UAAI,CAAC,IAAI,YAAY,aAAa;AAChC,YAAI,YAAY,cAAc;AAAA,UAC5B,QAAQ,YAAY,eAAe,WAAW,OAAO;AAAA,UACrD,SAAS,YAAY,oBAAoB;AAAA,UACzC,MAAM,YAAY,WAAW;AAAA,UAC7B,WAAW,YAAY,WAAW;AAAA,UAClC,MAAM;AAAA;AAER,YAAI,YAAY,YAAY,SAAS;AAAA;AAIvC,UAAI,YAAY,YAAY,OAAO,KAAK;AAAA,QACtC,WAAW,YAAY;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO,YAAY;AAAA,QACnB,SAAS;AAAA;AAAA;AAIb,UAAM,gBAAgB;AACtB,UAAM,UAAU,OAAO,KAAK;AAC5B,eAAW,UAAU,SAAS;AAC5B,YAAM,UAAU;AAChB,cAAQ,SAAS,IAAI,QAAQ;AAG7B,UAAI,IAAI,QAAQ,KAAK,MAAM,gBAAgB;AACzC,YAAI,QAAQ,OAAO,MAAM,UAAU,SAAS,IAAI,QAAQ,WAAW;AAAA;AAErE,oBAAc,KAAK,IAAI;AAAA;AAEzB,WAAO;AAAA;AAAA,EAGT,kBAAkB,SAAS,UAAU;AACnC,QAAI,KAAK,YAAY,QAAQ,SAAS,GAAG;AACvC,UAAI,iBAAiB,QAAQ,IAAI;AAE/B,gBAAQ,GAAG,KAAK,MAAM,uBAAuB,QAAQ,GAAG;AACxD,eAAO,QAAQ,GAAG;AAAA;AAGpB,YAAM,qBAAqB,KAAK,MAAM;AACtC,UAAI,0BAA0B,MAC5B,KAAK;AAEP,UACE,OAAO,UAAU,eAAe,KAAK,KAAK,MAAM,eAAe,uBAC/D,KAAK,MAAM,cAAc,oBAAoB,UAAU;AAEvD,kCAA0B,KAAK,MAAM,cAAc,oBAAoB;AAEzE,WAAK,MAAM,WAAW,QAAQ,GAAG,KAAK;AACtC,WAAK,MAAM,YAAY,SAAS,KAAK;AACrC,WAAK,MAAM,WAAW,QAAQ,GAAG;AACjC,WAAK,MAAM,2BAA2B,WAAW,QAAQ,GAAG;AAE5D,WAAK,SAAS,sBAAsB;AAAA;AAAA;AAAA;", "names": []}