{"version": 3, "sources": ["../../../src/dialects/oracle/connection-manager.js"], "sourcesContent": ["// Copyright (c) 2022, Oracle and/or its affiliates. All rights reserved\n\n'use strict';\n\nconst AbstractConnectionManager = require('../abstract/connection-manager');\nconst SequelizeErrors = require('../../errors');\nconst parserStore = require('../parserStore')('oracle');\nconst { logger } = require('../../utils/logger');\nconst semver = require('semver');\nconst debug = logger.debugContext('connection:oracle');\nconst DataTypes = require('../../data-types').oracle;\nconst { promisify } = require('util');\n/**\n * Oracle Connection Manager\n *\n * Get connections, validate and disconnect them.\n * AbstractConnectionManager pooling use it to handle Oracle specific connections\n * Use github.com/oracle/node-oracledb to connect with Oracle server\n *\n * @private\n */\nexport class OracleConnectionManager extends AbstractConnectionManager {\n  constructor(dialect, sequelize) {\n    super(dialect, sequelize);\n\n    this.sequelize = sequelize;\n    this.sequelize.config.port = this.sequelize.config.port || 1521;\n    this.lib = this._loadDialectModule('oracledb');\n    this.extendLib();\n    this.refreshTypeParser(DataTypes);\n  }\n\n  /**\n   * Method for initializing the lib\n   *\n   */\n  extendLib() {\n    if (this.sequelize.config && 'dialectOptions' in this.sequelize.config) {\n      const dialectOptions = this.sequelize.config.dialectOptions;\n      if (dialectOptions && 'maxRows' in dialectOptions) {\n        this.lib.maxRows = this.sequelize.config.dialectOptions.maxRows;\n      }\n      if (dialectOptions && 'fetchAsString' in dialectOptions) {\n        this.lib.fetchAsString = this.sequelize.config.dialectOptions.fetchAsString;\n      } else {\n        this.lib.fetchAsString = [this.lib.CLOB];\n      }\n    }\n    // Retrieve BLOB always as Buffer.\n    this.lib.fetchAsBuffer = [this.lib.BLOB];\n  }\n\n  /**\n   * Method for checking the config object passed and generate the full database if not fully passed\n   * With dbName, host and port, it generates a string like this : 'host:port/dbname'\n   *\n   * @param {object} config\n   * @returns {Promise<Connection>}\n   * @private\n   */\n  buildConnectString(config) {\n    if (!config.host || config.host.length === 0)\n      return config.database;\n    let connectString = config.host;\n    if (config.port && config.port > 0) {\n      connectString += `:${config.port}`;\n    } else {\n      connectString += ':1521';\n    }\n    if (config.database && config.database.length > 0) {\n      connectString += `/${config.database}`;\n    }\n    return connectString;\n  }\n\n  // Expose this as a method so that the parsing may be updated when the user has added additional, custom types\n  _refreshTypeParser(dataType) {\n    parserStore.refresh(dataType);\n  }\n\n  _clearTypeParser() {\n    parserStore.clear();\n  }\n\n  /**\n   * Connect with Oracle database based on config, Handle any errors in connection\n   * Set the pool handlers on connection.error\n   * Also set proper timezone once connection is connected.\n   *\n   * @param {object} config\n   * @returns {Promise<Connection>}\n   * @private\n   */\n  async connect(config) {\n    const connectionConfig = {\n      user: config.username,\n      password: config.password,\n      externalAuth: config.externalAuth,\n      stmtCacheSize: 0,\n      connectString: this.buildConnectString(config),\n      ...config.dialectOptions\n    };\n\n    try {\n      const connection = await this.lib.getConnection(connectionConfig);\n      // Setting the sequelize database version to Oracle DB server version to remove the roundtrip for DB version query\n      this.sequelize.options.databaseVersion = semver.coerce(connection.oracleServerVersionString).version;\n\n      debug('connection acquired');\n      connection.on('error', error => {\n        switch (error.code) {\n          case 'ESOCKET':\n          case 'ECONNRESET':\n          case 'EPIPE':\n          case 'PROTOCOL_CONNECTION_LOST':\n            this.pool.destroy(connection);\n        }\n      });\n\n      return connection;\n    } catch (err) {\n      // We split to get the error number; it comes as ORA-XXXXX:\n      let errorCode = err.message.split(':');\n      errorCode = errorCode[0];\n\n      switch (errorCode) {\n        case 'ORA-12560': // ORA-12560: TNS: Protocol Adapter Error\n        case 'ORA-12154': // ORA-12154: TNS: Could not resolve the connect identifier specified\n        case 'ORA-12505': // ORA-12505: TNS: Listener does not currently know of SID given in connect descriptor\n        case 'ORA-12514': // ORA-12514: TNS: Listener does not currently know of service requested in connect descriptor\n        case 'NJS-511': // NJS-511: connection refused\n        case 'NJS-516': // NJS-516: No Config Dir\n        case 'NJS-517': // NJS-517: TNS Entry not found\n        case 'NJS-520': // NJS-520: TNS Names File missing  \n          throw new SequelizeErrors.ConnectionRefusedError(err);\n        case 'ORA-28000': // ORA-28000: Account locked\n        case 'ORA-28040': // ORA-28040: No matching authentication protocol\n        case 'ORA-01017': // ORA-01017: invalid username/password; logon denied\n        case 'NJS-506': // NJS-506: TLS Auth Failure\n          throw new SequelizeErrors.AccessDeniedError(err);\n        case 'ORA-12541': // ORA-12541: TNS: No listener\n        case 'NJS-503': // NJS-503: Connection Incomplete\n        case 'NJS-508': // NJS-508: TLS HOST MATCH Failure\n        case 'NJS-507': // NJS-507: TLS DN MATCH Failure\n          throw new SequelizeErrors.HostNotReachableError(err);\n        case 'NJS-512': // NJS-512: Invalid Connect String Parameters\n        case 'NJS-515': // NJS-515: Invalid EZCONNECT Syntax\n        case 'NJS-518': // NJS-518: Invald ServiceName\n        case 'NJS-519': // NJS-519: Invald SID\n          throw new SequelizeErrors.InvalidConnectionError(err);\n        case 'ORA-12170': // ORA-12170: TNS: Connect Timeout occurred\n        case 'NJS-510': // NJS-510: Connect Timeout occurred\n\n          throw new SequelizeErrors.ConnectionTimedOutError(err);\n        default:\n          throw new SequelizeErrors.ConnectionError(err);\n      }\n    }\n  }\n\n  async disconnect(connection) {\n    if (!connection.isHealthy()) {\n      debug('connection tried to disconnect but was already at CLOSED state');\n      return;\n    }\n\n    return await promisify(callback => connection.close(callback))();\n  }\n\n  /**\n   * Checking if the connection object is valid and the connection is healthy\n   *\n   * @param {object} connection\n   * @private\n   */\n  validate(connection) {\n    return connection && connection.isHealthy();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAIA,MAAM,4BAA4B,QAAQ;AAC1C,MAAM,kBAAkB,QAAQ;AAChC,MAAM,cAAc,QAAQ,kBAAkB;AAC9C,MAAM,EAAE,WAAW,QAAQ;AAC3B,MAAM,SAAS,QAAQ;AACvB,MAAM,QAAQ,OAAO,aAAa;AAClC,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,EAAE,cAAc,QAAQ;AAUvB,sCAAsC,0BAA0B;AAAA,EACrE,YAAY,SAAS,WAAW;AAC9B,UAAM,SAAS;AAEf,SAAK,YAAY;AACjB,SAAK,UAAU,OAAO,OAAO,KAAK,UAAU,OAAO,QAAQ;AAC3D,SAAK,MAAM,KAAK,mBAAmB;AACnC,SAAK;AACL,SAAK,kBAAkB;AAAA;AAAA,EAOzB,YAAY;AACV,QAAI,KAAK,UAAU,UAAU,oBAAoB,KAAK,UAAU,QAAQ;AACtE,YAAM,iBAAiB,KAAK,UAAU,OAAO;AAC7C,UAAI,kBAAkB,aAAa,gBAAgB;AACjD,aAAK,IAAI,UAAU,KAAK,UAAU,OAAO,eAAe;AAAA;AAE1D,UAAI,kBAAkB,mBAAmB,gBAAgB;AACvD,aAAK,IAAI,gBAAgB,KAAK,UAAU,OAAO,eAAe;AAAA,aACzD;AACL,aAAK,IAAI,gBAAgB,CAAC,KAAK,IAAI;AAAA;AAAA;AAIvC,SAAK,IAAI,gBAAgB,CAAC,KAAK,IAAI;AAAA;AAAA,EAWrC,mBAAmB,QAAQ;AACzB,QAAI,CAAC,OAAO,QAAQ,OAAO,KAAK,WAAW;AACzC,aAAO,OAAO;AAChB,QAAI,gBAAgB,OAAO;AAC3B,QAAI,OAAO,QAAQ,OAAO,OAAO,GAAG;AAClC,uBAAiB,IAAI,OAAO;AAAA,WACvB;AACL,uBAAiB;AAAA;AAEnB,QAAI,OAAO,YAAY,OAAO,SAAS,SAAS,GAAG;AACjD,uBAAiB,IAAI,OAAO;AAAA;AAE9B,WAAO;AAAA;AAAA,EAIT,mBAAmB,UAAU;AAC3B,gBAAY,QAAQ;AAAA;AAAA,EAGtB,mBAAmB;AACjB,gBAAY;AAAA;AAAA,QAYR,QAAQ,QAAQ;AACpB,UAAM,mBAAmB;AAAA,MACvB,MAAM,OAAO;AAAA,MACb,UAAU,OAAO;AAAA,MACjB,cAAc,OAAO;AAAA,MACrB,eAAe;AAAA,MACf,eAAe,KAAK,mBAAmB;AAAA,OACpC,OAAO;AAGZ,QAAI;AACF,YAAM,aAAa,MAAM,KAAK,IAAI,cAAc;AAEhD,WAAK,UAAU,QAAQ,kBAAkB,OAAO,OAAO,WAAW,2BAA2B;AAE7F,YAAM;AACN,iBAAW,GAAG,SAAS,WAAS;AAC9B,gBAAQ,MAAM;AAAA,eACP;AAAA,eACA;AAAA,eACA;AAAA,eACA;AACH,iBAAK,KAAK,QAAQ;AAAA;AAAA;AAIxB,aAAO;AAAA,aACA,KAAP;AAEA,UAAI,YAAY,IAAI,QAAQ,MAAM;AAClC,kBAAY,UAAU;AAEtB,cAAQ;AAAA,aACD;AAAA,aACA;AAAA,aACA;AAAA,aACA;AAAA,aACA;AAAA,aACA;AAAA,aACA;AAAA,aACA;AACH,gBAAM,IAAI,gBAAgB,uBAAuB;AAAA,aAC9C;AAAA,aACA;AAAA,aACA;AAAA,aACA;AACH,gBAAM,IAAI,gBAAgB,kBAAkB;AAAA,aACzC;AAAA,aACA;AAAA,aACA;AAAA,aACA;AACH,gBAAM,IAAI,gBAAgB,sBAAsB;AAAA,aAC7C;AAAA,aACA;AAAA,aACA;AAAA,aACA;AACH,gBAAM,IAAI,gBAAgB,uBAAuB;AAAA,aAC9C;AAAA,aACA;AAEH,gBAAM,IAAI,gBAAgB,wBAAwB;AAAA;AAElD,gBAAM,IAAI,gBAAgB,gBAAgB;AAAA;AAAA;AAAA;AAAA,QAK5C,WAAW,YAAY;AAC3B,QAAI,CAAC,WAAW,aAAa;AAC3B,YAAM;AACN;AAAA;AAGF,WAAO,MAAM,UAAU,cAAY,WAAW,MAAM;AAAA;AAAA,EAStD,SAAS,YAAY;AACnB,WAAO,cAAc,WAAW;AAAA;AAAA;", "names": []}