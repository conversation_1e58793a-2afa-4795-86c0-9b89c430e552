{"version": 3, "sources": ["../src/table-hints.js"], "sourcesContent": ["'use strict';\n\n/**\n * An enum of table hints to be used in mssql for querying with table hints\n *\n * @property NOLOCK\n * @property READUNCOMMITTED\n * @property UPDLOCK\n * @property REPEATABLEREAD\n * @property SERIALIZABLE\n * @property READCOMMITTED\n * @property TABLOCK\n * @property TABLOCKX\n * @property PAGLOCK\n * @property ROWLOCK\n * @property NOWAIT\n * @property READPAST\n * @property XLOCK\n * @property SNAPSHOT\n * @property NOEXPAND\n */\nconst TableHints = module.exports = { // eslint-disable-line\n  NOLOCK: 'NOLOCK',\n  READUNCOMMITTED: 'READUNCOMMITTED',\n  UPDLOCK: 'UPDLOCK',\n  REPEATABLEREAD: 'REPEATABLEREAD',\n  SERIALIZABLE: 'SERIALIZABLE',\n  READCOMMITTED: 'READCOMMITTED',\n  TABLOCK: 'TABLOCK',\n  TABLOCKX: 'TABLOCKX',\n  PAGLOCK: 'PAGLOCK',\n  ROWLOCK: 'ROWLOCK',\n  NO<PERSON><PERSON>: 'NOWAIT',\n  READPAST: 'READPAST',\n  <PERSON>L<PERSON><PERSON>: 'XLOCK',\n  SNAPSHOT: 'SNAPSHOT',\n  NOEXPAND: 'NOEXPAND'\n};\n"], "mappings": ";AAqBA,MAAM,aAAa,OAAO,UAAU;AAAA,EAClC,QAAQ;AAAA,EACR,iBAAiB;AAAA,EACjB,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA;", "names": []}