{"version": 3, "sources": ["../src/utils.js"], "sourcesContent": ["'use strict';\n\nconst DataTypes = require('./data-types');\nconst SqlString = require('./sql-string');\nconst _ = require('lodash');\nconst baseIsNative = require('lodash/_baseIsNative');\nconst uuidv1 = require('uuid').v1;\nconst uuidv4 = require('uuid').v4;\nconst operators = require('./operators');\nconst operatorsSet = new Set(Object.values(operators));\n\nlet inflection = require('inflection');\n\nexports.classToInvokable = require('./utils/class-to-invokable').classToInvokable;\nexports.joinSQLFragments = require('./utils/join-sql-fragments').joinSQLFragments;\n\nfunction useInflection(_inflection) {\n  inflection = _inflection;\n}\nexports.useInflection = useInflection;\n\nfunction camelizeIf(str, condition) {\n  let result = str;\n\n  if (condition) {\n    result = camelize(str);\n  }\n\n  return result;\n}\nexports.camelizeIf = camelizeIf;\n\nfunction underscoredIf(str, condition) {\n  let result = str;\n\n  if (condition) {\n    result = underscore(str);\n  }\n\n  return result;\n}\nexports.underscoredIf = underscoredIf;\n\nfunction isPrimitive(val) {\n  const type = typeof val;\n  return ['string', 'number', 'boolean'].includes(type);\n}\nexports.isPrimitive = isPrimitive;\n\n// Same concept as _.merge, but don't overwrite properties that have already been assigned\nfunction mergeDefaults(a, b) {\n  return _.mergeWith(a, b, (objectValue, sourceValue) => {\n    // If it's an object, let _ handle it this time, we will be called again for each property\n    if (!_.isPlainObject(objectValue) && objectValue !== undefined) {\n      // _.isNative includes a check for core-js and throws an error if present.\n      // Depending on _baseIsNative bypasses the core-js check.\n      if (_.isFunction(objectValue) && baseIsNative(objectValue)) {\n        return sourceValue || objectValue;\n      }\n      return objectValue;\n    }\n  });\n}\nexports.mergeDefaults = mergeDefaults;\n\n// An alternative to _.merge, which doesn't clone its arguments\n// Cloning is a bad idea because options arguments may contain references to sequelize\n// models - which again reference database libs which don't like to be cloned (in particular pg-native)\nfunction merge() {\n  const result = {};\n\n  for (const obj of arguments) {\n    _.forOwn(obj, (value, key) => {\n      if (value !== undefined) {\n        if (!result[key]) {\n          result[key] = value;\n        } else if (_.isPlainObject(value) && _.isPlainObject(result[key])) {\n          result[key] = merge(result[key], value);\n        } else if (Array.isArray(value) && Array.isArray(result[key])) {\n          result[key] = value.concat(result[key]);\n        } else {\n          result[key] = value;\n        }\n      }\n    });\n  }\n\n  return result;\n}\nexports.merge = merge;\n\nfunction spliceStr(str, index, count, add) {\n  return str.slice(0, index) + add + str.slice(index + count);\n}\nexports.spliceStr = spliceStr;\n\nfunction camelize(str) {\n  return str.trim().replace(/[-_\\s]+(.)?/g, (match, c) => c.toUpperCase());\n}\nexports.camelize = camelize;\n\nfunction underscore(str) {\n  return inflection.underscore(str);\n}\nexports.underscore = underscore;\n\nfunction singularize(str) {\n  return inflection.singularize(str);\n}\nexports.singularize = singularize;\n\nfunction pluralize(str) {\n  return inflection.pluralize(str);\n}\nexports.pluralize = pluralize;\n\n/**\n * @deprecated use {@link injectReplacements} instead. This method has been removed in v7.\n *\n * @param {unknown[]} arr - first item is the SQL, following items are the positional replacements.\n * @param {AbstractDialect} dialect\n */\nfunction format(arr, dialect) {\n  const timeZone = null;\n  // Make a clone of the array beacuse format modifies the passed args\n  return SqlString.format(arr[0], arr.slice(1), timeZone, dialect);\n}\nexports.format = format;\n\n/**\n * @deprecated use {@link injectReplacements} instead. This method has been removed in v7.\n *\n * @param {string} sql\n * @param {object} parameters\n * @param {AbstractDialect} dialect\n */\nfunction formatNamedParameters(sql, parameters, dialect) {\n  const timeZone = null;\n  return SqlString.formatNamedParameters(sql, parameters, timeZone, dialect);\n}\nexports.formatNamedParameters = formatNamedParameters;\n\nfunction cloneDeep(obj, onlyPlain) {\n  obj = obj || {};\n  return _.cloneDeepWith(obj, elem => {\n    // Do not try to customize cloning of arrays or POJOs\n    if (Array.isArray(elem) || _.isPlainObject(elem)) {\n      return undefined;\n    }\n\n    // If we specified to clone only plain objects & arrays, we ignore everyhing else\n    // In any case, don't clone stuff that's an object, but not a plain one - fx example sequelize models and instances\n    if (onlyPlain || typeof elem === 'object') {\n      return elem;\n    }\n\n    // Preserve special data-types like `fn` across clones. _.get() is used for checking up the prototype chain\n    if (elem && typeof elem.clone === 'function') {\n      return elem.clone();\n    }\n  });\n}\nexports.cloneDeep = cloneDeep;\n\n/* Expand and normalize finder options */\nfunction mapFinderOptions(options, Model) {\n  if (options.attributes && Array.isArray(options.attributes)) {\n    options.attributes = Model._injectDependentVirtualAttributes(options.attributes);\n    options.attributes = options.attributes.filter(v => !Model._virtualAttributes.has(v));\n  }\n\n  mapOptionFieldNames(options, Model);\n\n  return options;\n}\nexports.mapFinderOptions = mapFinderOptions;\n\n/* Used to map field names in attributes and where conditions */\nfunction mapOptionFieldNames(options, Model) {\n  if (Array.isArray(options.attributes)) {\n    options.attributes = options.attributes.map(attr => {\n      // Object lookups will force any variable to strings, we don't want that for special objects etc\n      if (typeof attr !== 'string') return attr;\n      // Map attributes to aliased syntax attributes\n      if (Model.rawAttributes[attr] && attr !== Model.rawAttributes[attr].field) {\n        return [Model.rawAttributes[attr].field, attr];\n      }\n      return attr;\n    });\n  }\n\n  if (options.where && _.isPlainObject(options.where)) {\n    options.where = mapWhereFieldNames(options.where, Model);\n  }\n\n  return options;\n}\nexports.mapOptionFieldNames = mapOptionFieldNames;\n\nfunction mapWhereFieldNames(attributes, Model) {\n  if (attributes) {\n    attributes = cloneDeep(attributes);\n    getComplexKeys(attributes).forEach(attribute => {\n      const rawAttribute = Model.rawAttributes[attribute];\n\n      if (rawAttribute && rawAttribute.field !== rawAttribute.fieldName) {\n        attributes[rawAttribute.field] = attributes[attribute];\n        delete attributes[attribute];\n      }\n\n      if (_.isPlainObject(attributes[attribute])\n        && !(rawAttribute && (\n          rawAttribute.type instanceof DataTypes.HSTORE\n          || rawAttribute.type instanceof DataTypes.JSON))) { // Prevent renaming of HSTORE & JSON fields\n        attributes[attribute] = mapOptionFieldNames({\n          where: attributes[attribute]\n        }, Model).where;\n      }\n\n      if (Array.isArray(attributes[attribute])) {\n        attributes[attribute].forEach((where, index) => {\n          if (_.isPlainObject(where)) {\n            attributes[attribute][index] = mapWhereFieldNames(where, Model);\n          }\n        });\n      }\n\n    });\n  }\n\n  return attributes;\n}\nexports.mapWhereFieldNames = mapWhereFieldNames;\n\n/* Used to map field names in values */\nfunction mapValueFieldNames(dataValues, fields, Model) {\n  const values = {};\n\n  for (const attr of fields) {\n    if (dataValues[attr] !== undefined && !Model._virtualAttributes.has(attr)) {\n      // Field name mapping\n      if (Model.rawAttributes[attr] && Model.rawAttributes[attr].field && Model.rawAttributes[attr].field !== attr) {\n        values[Model.rawAttributes[attr].field] = dataValues[attr];\n      } else {\n        values[attr] = dataValues[attr];\n      }\n    }\n  }\n\n  return values;\n}\nexports.mapValueFieldNames = mapValueFieldNames;\n\nfunction isColString(value) {\n  return typeof value === 'string' && value[0] === '$' && value[value.length - 1] === '$';\n}\nexports.isColString = isColString;\n\nfunction canTreatArrayAsAnd(arr) {\n  return arr.some(arg => _.isPlainObject(arg) || arg instanceof Where);\n}\nexports.canTreatArrayAsAnd = canTreatArrayAsAnd;\n\nfunction combineTableNames(tableName1, tableName2) {\n  return tableName1.toLowerCase() < tableName2.toLowerCase() ? tableName1 + tableName2 : tableName2 + tableName1;\n}\nexports.combineTableNames = combineTableNames;\n\nfunction toDefaultValue(value, dialect) {\n  if (typeof value === 'function') {\n    const tmp = value();\n    if (tmp instanceof DataTypes.ABSTRACT) {\n      return tmp.toSql();\n    }\n    return tmp;\n  }\n  if (value instanceof DataTypes.UUIDV1) {\n    return uuidv1();\n  }\n  if (value instanceof DataTypes.UUIDV4) {\n    return uuidv4();\n  }\n  if (value instanceof DataTypes.NOW) {\n    return now(dialect);\n  }\n  if (Array.isArray(value)) {\n    return value.slice();\n  }\n  if (_.isPlainObject(value)) {\n    return { ...value };\n  }\n  return value;\n}\nexports.toDefaultValue = toDefaultValue;\n\n/**\n * Determine if the default value provided exists and can be described\n * in a db schema using the DEFAULT directive.\n *\n * @param  {*} value Any default value.\n * @returns {boolean} yes / no.\n * @private\n */\nfunction defaultValueSchemable(value) {\n  if (value === undefined) { return false; }\n\n  // TODO this will be schemable when all supported db\n  // have been normalized for this case\n  if (value instanceof DataTypes.NOW) { return false; }\n\n  if (value instanceof DataTypes.UUIDV1 || value instanceof DataTypes.UUIDV4) { return false; }\n\n  return typeof value !== 'function';\n}\nexports.defaultValueSchemable = defaultValueSchemable;\n\nfunction removeNullValuesFromHash(hash, omitNull, options) {\n  let result = hash;\n\n  options = options || {};\n  options.allowNull = options.allowNull || [];\n\n  if (omitNull) {\n    const _hash = {};\n\n    _.forIn(hash, (val, key) => {\n      if (options.allowNull.includes(key) || key.endsWith('Id') || val !== null && val !== undefined) {\n        _hash[key] = val;\n      }\n    });\n\n    result = _hash;\n  }\n\n  return result;\n}\nexports.removeNullValuesFromHash = removeNullValuesFromHash;\n\nconst dialects = new Set(['mariadb', 'mysql', 'postgres', 'sqlite', 'mssql', 'db2', 'oracle']);\n\nfunction now(dialect) {\n  const d = new Date();\n  if (!dialects.has(dialect)) {\n    d.setMilliseconds(0);\n  }\n  return d;\n}\nexports.now = now;\n\n// Note: Use the `quoteIdentifier()` and `escape()` methods on the\n// `QueryInterface` instead for more portable code.\n\nconst TICK_CHAR = '`';\nexports.TICK_CHAR = TICK_CHAR;\n\nfunction addTicks(s, tickChar) {\n  tickChar = tickChar || TICK_CHAR;\n  return tickChar + removeTicks(s, tickChar) + tickChar;\n}\nexports.addTicks = addTicks;\n\nfunction removeTicks(s, tickChar) {\n  tickChar = tickChar || TICK_CHAR;\n  return s.replace(new RegExp(tickChar, 'g'), '');\n}\nexports.removeTicks = removeTicks;\n\n/**\n * Receives a tree-like object and returns a plain object which depth is 1.\n *\n * - Input:\n *\n *  {\n *    name: 'John',\n *    address: {\n *      street: 'Fake St. 123',\n *      coordinates: {\n *        longitude: 55.6779627,\n *        latitude: 12.5964313\n *      }\n *    }\n *  }\n *\n * - Output:\n *\n *  {\n *    name: 'John',\n *    address.street: 'Fake St. 123',\n *    address.coordinates.latitude: 55.6779627,\n *    address.coordinates.longitude: 12.5964313\n *  }\n *\n * @param {object} value an Object\n * @returns {object} a flattened object\n * @private\n */\nfunction flattenObjectDeep(value) {\n  if (!_.isPlainObject(value)) return value;\n  const flattenedObj = {};\n\n  function flattenObject(obj, subPath) {\n    Object.keys(obj).forEach(key => {\n      const pathToProperty = subPath ? `${subPath}.${key}` : key;\n      if (typeof obj[key] === 'object' && obj[key] !== null) {\n        flattenObject(obj[key], pathToProperty);\n      } else {\n        flattenedObj[pathToProperty] = _.get(obj, key);\n      }\n    });\n    return flattenedObj;\n  }\n\n  return flattenObject(value, undefined);\n}\nexports.flattenObjectDeep = flattenObjectDeep;\n\n/**\n * Utility functions for representing SQL functions, and columns that should be escaped.\n * Please do not use these functions directly, use Sequelize.fn and Sequelize.col instead.\n *\n * @private\n */\nclass SequelizeMethod {}\nexports.SequelizeMethod = SequelizeMethod;\n\nclass Fn extends SequelizeMethod {\n  constructor(fn, args) {\n    super();\n    this.fn = fn;\n    this.args = args;\n  }\n  clone() {\n    return new Fn(this.fn, this.args);\n  }\n}\nexports.Fn = Fn;\n\nclass Col extends SequelizeMethod {\n  constructor(col, ...args) {\n    super();\n    if (args.length > 0) {\n      col = args;\n    }\n    this.col = col;\n  }\n}\nexports.Col = Col;\n\nclass Cast extends SequelizeMethod {\n  constructor(val, type, json) {\n    super();\n    this.val = val;\n    this.type = (type || '').trim();\n    this.json = json || false;\n  }\n}\nexports.Cast = Cast;\n\nclass Literal extends SequelizeMethod {\n  constructor(val) {\n    super();\n    this.val = val;\n  }\n}\nexports.Literal = Literal;\n\nclass Json extends SequelizeMethod {\n  constructor(conditionsOrPath, value) {\n    super();\n    if (_.isObject(conditionsOrPath)) {\n      this.conditions = conditionsOrPath;\n    } else {\n      this.path = conditionsOrPath;\n      if (value) {\n        this.value = value;\n      }\n    }\n  }\n}\nexports.Json = Json;\n\nclass Where extends SequelizeMethod {\n  constructor(attribute, comparator, logic) {\n    super();\n    if (logic === undefined) {\n      logic = comparator;\n      comparator = '=';\n    }\n\n    this.attribute = attribute;\n    this.comparator = comparator;\n    this.logic = logic;\n  }\n}\nexports.Where = Where;\n\n//Collection of helper methods to make it easier to work with symbol operators\n\n/**\n * getOperators\n *\n * @param  {object} obj\n * @returns {Array<symbol>} All operators properties of obj\n * @private\n */\nfunction getOperators(obj) {\n  return Object.getOwnPropertySymbols(obj).filter(s => operatorsSet.has(s));\n}\nexports.getOperators = getOperators;\n\n/**\n * getComplexKeys\n *\n * @param  {object} obj\n * @returns {Array<string|symbol>} All keys including operators\n * @private\n */\nfunction getComplexKeys(obj) {\n  return getOperators(obj).concat(Object.keys(obj));\n}\nexports.getComplexKeys = getComplexKeys;\n\n/**\n * getComplexSize\n *\n * @param  {object|Array} obj\n * @returns {number}      Length of object properties including operators if obj is array returns its length\n * @private\n */\nfunction getComplexSize(obj) {\n  return Array.isArray(obj) ? obj.length : getComplexKeys(obj).length;\n}\nexports.getComplexSize = getComplexSize;\n\n/**\n * Returns true if a where clause is empty, even with Symbols\n *\n * @param  {object} obj\n * @returns {boolean}\n * @private\n */\nfunction isWhereEmpty(obj) {\n  return !!obj && _.isEmpty(obj) && getOperators(obj).length === 0;\n}\nexports.isWhereEmpty = isWhereEmpty;\n\n/**\n * Returns ENUM name by joining table and column name\n *\n * @param {string} tableName\n * @param {string} columnName\n * @returns {string}\n * @private\n */\nfunction generateEnumName(tableName, columnName) {\n  return `enum_${tableName}_${columnName}`;\n}\nexports.generateEnumName = generateEnumName;\n\n/**\n * Returns an new Object which keys are camelized\n *\n * @param {object} obj\n * @returns {string}\n * @private\n */\nfunction camelizeObjectKeys(obj) {\n  const newObj = new Object();\n  Object.keys(obj).forEach(key => {\n    newObj[camelize(key)] = obj[key];\n  });\n  return newObj;\n}\nexports.camelizeObjectKeys = camelizeObjectKeys;\n\n/**\n * Assigns own and inherited enumerable string and symbol keyed properties of source\n * objects to the destination object.\n *\n * https://lodash.com/docs/4.17.4#defaults\n *\n * **Note:** This method mutates `object`.\n *\n * @param {object} object The destination object.\n * @param {...object} [sources] The source objects.\n * @returns {object} Returns `object`.\n * @private\n */\nfunction defaults(object, ...sources) {\n  object = Object(object);\n\n  sources.forEach(source => {\n    if (source) {\n      source = Object(source);\n\n      getComplexKeys(source).forEach(key => {\n        const value = object[key];\n        if (\n          value === undefined ||\n            _.eq(value, Object.prototype[key]) &&\n            !Object.prototype.hasOwnProperty.call(object, key)\n\n        ) {\n          object[key] = source[key];\n        }\n      });\n    }\n  });\n\n  return object;\n}\nexports.defaults = defaults;\n\n/**\n *\n * @param {object} index\n * @param {Array}  index.fields\n * @param {string} [index.name]\n * @param {string|object} tableName\n *\n * @returns {object}\n * @private\n */\nfunction nameIndex(index, tableName) {\n  if (tableName.tableName) tableName = tableName.tableName;\n\n  if (!Object.prototype.hasOwnProperty.call(index, 'name')) {\n    const fields = index.fields.map(\n      field => typeof field === 'string' ? field : field.name || field.attribute\n    );\n    index.name = underscore(`${tableName}_${fields.join('_')}`);\n  }\n\n  return index;\n}\nexports.nameIndex = nameIndex;\n\n/**\n * Checks if 2 arrays intersect.\n *\n * @param {Array} arr1\n * @param {Array} arr2\n * @private\n */\nfunction intersects(arr1, arr2) {\n  return arr1.some(v => arr2.includes(v));\n}\nexports.intersects = intersects;\n\n/**\n * Stringify a value as JSON with some differences:\n * - bigints are stringified as a json string. (`safeStringifyJson({ val: 1n })` outputs `'{ \"val\": \"1\" }'`).\n *   This is because of a decision by TC39 to not support bigint in JSON.stringify https://github.com/tc39/proposal-bigint/issues/24\n *\n * @param {any} value the value to stringify.\n * @returns {string} the resulting json.\n */\nfunction safeStringifyJson(value /* : any */) /* : string */ {\n  return JSON.stringify(value, (key, value) => {\n    if (typeof value === 'bigint') {\n      return String(value);\n    }\n\n    return value;\n  });\n}\n\nexports.safeStringifyJson = safeStringifyJson;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,MAAM,YAAY,QAAQ;AAC1B,MAAM,YAAY,QAAQ;AAC1B,MAAM,IAAI,QAAQ;AAClB,MAAM,eAAe,QAAQ;AAC7B,MAAM,SAAS,QAAQ,QAAQ;AAC/B,MAAM,SAAS,QAAQ,QAAQ;AAC/B,MAAM,YAAY,QAAQ;AAC1B,MAAM,eAAe,IAAI,IAAI,OAAO,OAAO;AAE3C,IAAI,aAAa,QAAQ;AAEzB,QAAQ,mBAAmB,QAAQ,8BAA8B;AACjE,QAAQ,mBAAmB,QAAQ,8BAA8B;AAEjE,uBAAuB,aAAa;AAClC,eAAa;AAAA;AAEf,QAAQ,gBAAgB;AAExB,oBAAoB,KAAK,WAAW;AAClC,MAAI,SAAS;AAEb,MAAI,WAAW;AACb,aAAS,SAAS;AAAA;AAGpB,SAAO;AAAA;AAET,QAAQ,aAAa;AAErB,uBAAuB,KAAK,WAAW;AACrC,MAAI,SAAS;AAEb,MAAI,WAAW;AACb,aAAS,WAAW;AAAA;AAGtB,SAAO;AAAA;AAET,QAAQ,gBAAgB;AAExB,qBAAqB,KAAK;AACxB,QAAM,OAAO,OAAO;AACpB,SAAO,CAAC,UAAU,UAAU,WAAW,SAAS;AAAA;AAElD,QAAQ,cAAc;AAGtB,uBAAuB,GAAG,GAAG;AAC3B,SAAO,EAAE,UAAU,GAAG,GAAG,CAAC,aAAa,gBAAgB;AAErD,QAAI,CAAC,EAAE,cAAc,gBAAgB,gBAAgB,QAAW;AAG9D,UAAI,EAAE,WAAW,gBAAgB,aAAa,cAAc;AAC1D,eAAO,eAAe;AAAA;AAExB,aAAO;AAAA;AAAA;AAAA;AAIb,QAAQ,gBAAgB;AAKxB,iBAAiB;AACf,QAAM,SAAS;AAEf,aAAW,OAAO,WAAW;AAC3B,MAAE,OAAO,KAAK,CAAC,OAAO,QAAQ;AAC5B,UAAI,UAAU,QAAW;AACvB,YAAI,CAAC,OAAO,MAAM;AAChB,iBAAO,OAAO;AAAA,mBACL,EAAE,cAAc,UAAU,EAAE,cAAc,OAAO,OAAO;AACjE,iBAAO,OAAO,MAAM,OAAO,MAAM;AAAA,mBACxB,MAAM,QAAQ,UAAU,MAAM,QAAQ,OAAO,OAAO;AAC7D,iBAAO,OAAO,MAAM,OAAO,OAAO;AAAA,eAC7B;AACL,iBAAO,OAAO;AAAA;AAAA;AAAA;AAAA;AAMtB,SAAO;AAAA;AAET,QAAQ,QAAQ;AAEhB,mBAAmB,KAAK,OAAO,OAAO,KAAK;AACzC,SAAO,IAAI,MAAM,GAAG,SAAS,MAAM,IAAI,MAAM,QAAQ;AAAA;AAEvD,QAAQ,YAAY;AAEpB,kBAAkB,KAAK;AACrB,SAAO,IAAI,OAAO,QAAQ,gBAAgB,CAAC,OAAO,MAAM,EAAE;AAAA;AAE5D,QAAQ,WAAW;AAEnB,oBAAoB,KAAK;AACvB,SAAO,WAAW,WAAW;AAAA;AAE/B,QAAQ,aAAa;AAErB,qBAAqB,KAAK;AACxB,SAAO,WAAW,YAAY;AAAA;AAEhC,QAAQ,cAAc;AAEtB,mBAAmB,KAAK;AACtB,SAAO,WAAW,UAAU;AAAA;AAE9B,QAAQ,YAAY;AAQpB,gBAAgB,KAAK,SAAS;AAC5B,QAAM,WAAW;AAEjB,SAAO,UAAU,OAAO,IAAI,IAAI,IAAI,MAAM,IAAI,UAAU;AAAA;AAE1D,QAAQ,SAAS;AASjB,+BAA+B,KAAK,YAAY,SAAS;AACvD,QAAM,WAAW;AACjB,SAAO,UAAU,sBAAsB,KAAK,YAAY,UAAU;AAAA;AAEpE,QAAQ,wBAAwB;AAEhC,mBAAmB,KAAK,WAAW;AACjC,QAAM,OAAO;AACb,SAAO,EAAE,cAAc,KAAK,UAAQ;AAElC,QAAI,MAAM,QAAQ,SAAS,EAAE,cAAc,OAAO;AAChD,aAAO;AAAA;AAKT,QAAI,aAAa,OAAO,SAAS,UAAU;AACzC,aAAO;AAAA;AAIT,QAAI,QAAQ,OAAO,KAAK,UAAU,YAAY;AAC5C,aAAO,KAAK;AAAA;AAAA;AAAA;AAIlB,QAAQ,YAAY;AAGpB,0BAA0B,SAAS,OAAO;AACxC,MAAI,QAAQ,cAAc,MAAM,QAAQ,QAAQ,aAAa;AAC3D,YAAQ,aAAa,MAAM,kCAAkC,QAAQ;AACrE,YAAQ,aAAa,QAAQ,WAAW,OAAO,OAAK,CAAC,MAAM,mBAAmB,IAAI;AAAA;AAGpF,sBAAoB,SAAS;AAE7B,SAAO;AAAA;AAET,QAAQ,mBAAmB;AAG3B,6BAA6B,SAAS,OAAO;AAC3C,MAAI,MAAM,QAAQ,QAAQ,aAAa;AACrC,YAAQ,aAAa,QAAQ,WAAW,IAAI,UAAQ;AAElD,UAAI,OAAO,SAAS;AAAU,eAAO;AAErC,UAAI,MAAM,cAAc,SAAS,SAAS,MAAM,cAAc,MAAM,OAAO;AACzE,eAAO,CAAC,MAAM,cAAc,MAAM,OAAO;AAAA;AAE3C,aAAO;AAAA;AAAA;AAIX,MAAI,QAAQ,SAAS,EAAE,cAAc,QAAQ,QAAQ;AACnD,YAAQ,QAAQ,mBAAmB,QAAQ,OAAO;AAAA;AAGpD,SAAO;AAAA;AAET,QAAQ,sBAAsB;AAE9B,4BAA4B,YAAY,OAAO;AAC7C,MAAI,YAAY;AACd,iBAAa,UAAU;AACvB,mBAAe,YAAY,QAAQ,eAAa;AAC9C,YAAM,eAAe,MAAM,cAAc;AAEzC,UAAI,gBAAgB,aAAa,UAAU,aAAa,WAAW;AACjE,mBAAW,aAAa,SAAS,WAAW;AAC5C,eAAO,WAAW;AAAA;AAGpB,UAAI,EAAE,cAAc,WAAW,eAC1B,CAAE,iBACH,cAAa,gBAAgB,UAAU,UACpC,aAAa,gBAAgB,UAAU,QAAQ;AACpD,mBAAW,aAAa,oBAAoB;AAAA,UAC1C,OAAO,WAAW;AAAA,WACjB,OAAO;AAAA;AAGZ,UAAI,MAAM,QAAQ,WAAW,aAAa;AACxC,mBAAW,WAAW,QAAQ,CAAC,OAAO,UAAU;AAC9C,cAAI,EAAE,cAAc,QAAQ;AAC1B,uBAAW,WAAW,SAAS,mBAAmB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAQnE,SAAO;AAAA;AAET,QAAQ,qBAAqB;AAG7B,4BAA4B,YAAY,QAAQ,OAAO;AACrD,QAAM,SAAS;AAEf,aAAW,QAAQ,QAAQ;AACzB,QAAI,WAAW,UAAU,UAAa,CAAC,MAAM,mBAAmB,IAAI,OAAO;AAEzE,UAAI,MAAM,cAAc,SAAS,MAAM,cAAc,MAAM,SAAS,MAAM,cAAc,MAAM,UAAU,MAAM;AAC5G,eAAO,MAAM,cAAc,MAAM,SAAS,WAAW;AAAA,aAChD;AACL,eAAO,QAAQ,WAAW;AAAA;AAAA;AAAA;AAKhC,SAAO;AAAA;AAET,QAAQ,qBAAqB;AAE7B,qBAAqB,OAAO;AAC1B,SAAO,OAAO,UAAU,YAAY,MAAM,OAAO,OAAO,MAAM,MAAM,SAAS,OAAO;AAAA;AAEtF,QAAQ,cAAc;AAEtB,4BAA4B,KAAK;AAC/B,SAAO,IAAI,KAAK,SAAO,EAAE,cAAc,QAAQ,eAAe;AAAA;AAEhE,QAAQ,qBAAqB;AAE7B,2BAA2B,YAAY,YAAY;AACjD,SAAO,WAAW,gBAAgB,WAAW,gBAAgB,aAAa,aAAa,aAAa;AAAA;AAEtG,QAAQ,oBAAoB;AAE5B,wBAAwB,OAAO,SAAS;AACtC,MAAI,OAAO,UAAU,YAAY;AAC/B,UAAM,MAAM;AACZ,QAAI,eAAe,UAAU,UAAU;AACrC,aAAO,IAAI;AAAA;AAEb,WAAO;AAAA;AAET,MAAI,iBAAiB,UAAU,QAAQ;AACrC,WAAO;AAAA;AAET,MAAI,iBAAiB,UAAU,QAAQ;AACrC,WAAO;AAAA;AAET,MAAI,iBAAiB,UAAU,KAAK;AAClC,WAAO,IAAI;AAAA;AAEb,MAAI,MAAM,QAAQ,QAAQ;AACxB,WAAO,MAAM;AAAA;AAEf,MAAI,EAAE,cAAc,QAAQ;AAC1B,WAAO,mBAAK;AAAA;AAEd,SAAO;AAAA;AAET,QAAQ,iBAAiB;AAUzB,+BAA+B,OAAO;AACpC,MAAI,UAAU,QAAW;AAAE,WAAO;AAAA;AAIlC,MAAI,iBAAiB,UAAU,KAAK;AAAE,WAAO;AAAA;AAE7C,MAAI,iBAAiB,UAAU,UAAU,iBAAiB,UAAU,QAAQ;AAAE,WAAO;AAAA;AAErF,SAAO,OAAO,UAAU;AAAA;AAE1B,QAAQ,wBAAwB;AAEhC,kCAAkC,MAAM,UAAU,SAAS;AACzD,MAAI,SAAS;AAEb,YAAU,WAAW;AACrB,UAAQ,YAAY,QAAQ,aAAa;AAEzC,MAAI,UAAU;AACZ,UAAM,QAAQ;AAEd,MAAE,MAAM,MAAM,CAAC,KAAK,QAAQ;AAC1B,UAAI,QAAQ,UAAU,SAAS,QAAQ,IAAI,SAAS,SAAS,QAAQ,QAAQ,QAAQ,QAAW;AAC9F,cAAM,OAAO;AAAA;AAAA;AAIjB,aAAS;AAAA;AAGX,SAAO;AAAA;AAET,QAAQ,2BAA2B;AAEnC,MAAM,WAAW,oBAAI,IAAI,CAAC,WAAW,SAAS,YAAY,UAAU,SAAS,OAAO;AAEpF,aAAa,SAAS;AACpB,QAAM,IAAI,IAAI;AACd,MAAI,CAAC,SAAS,IAAI,UAAU;AAC1B,MAAE,gBAAgB;AAAA;AAEpB,SAAO;AAAA;AAET,QAAQ,MAAM;AAKd,MAAM,YAAY;AAClB,QAAQ,YAAY;AAEpB,kBAAkB,GAAG,UAAU;AAC7B,aAAW,YAAY;AACvB,SAAO,WAAW,YAAY,GAAG,YAAY;AAAA;AAE/C,QAAQ,WAAW;AAEnB,qBAAqB,GAAG,UAAU;AAChC,aAAW,YAAY;AACvB,SAAO,EAAE,QAAQ,IAAI,OAAO,UAAU,MAAM;AAAA;AAE9C,QAAQ,cAAc;AA+BtB,2BAA2B,OAAO;AAChC,MAAI,CAAC,EAAE,cAAc;AAAQ,WAAO;AACpC,QAAM,eAAe;AAErB,yBAAuB,KAAK,SAAS;AACnC,WAAO,KAAK,KAAK,QAAQ,SAAO;AAC9B,YAAM,iBAAiB,UAAU,GAAG,WAAW,QAAQ;AACvD,UAAI,OAAO,IAAI,SAAS,YAAY,IAAI,SAAS,MAAM;AACrD,sBAAc,IAAI,MAAM;AAAA,aACnB;AACL,qBAAa,kBAAkB,EAAE,IAAI,KAAK;AAAA;AAAA;AAG9C,WAAO;AAAA;AAGT,SAAO,cAAc,OAAO;AAAA;AAE9B,QAAQ,oBAAoB;AAQ5B,sBAAsB;AAAA;AACtB,QAAQ,kBAAkB;AAE1B,iBAAiB,gBAAgB;AAAA,EAC/B,YAAY,IAAI,MAAM;AACpB;AACA,SAAK,KAAK;AACV,SAAK,OAAO;AAAA;AAAA,EAEd,QAAQ;AACN,WAAO,IAAI,GAAG,KAAK,IAAI,KAAK;AAAA;AAAA;AAGhC,QAAQ,KAAK;AAEb,kBAAkB,gBAAgB;AAAA,EAChC,YAAY,QAAQ,MAAM;AACxB;AACA,QAAI,KAAK,SAAS,GAAG;AACnB,YAAM;AAAA;AAER,SAAK,MAAM;AAAA;AAAA;AAGf,QAAQ,MAAM;AAEd,mBAAmB,gBAAgB;AAAA,EACjC,YAAY,KAAK,MAAM,MAAM;AAC3B;AACA,SAAK,MAAM;AACX,SAAK,OAAQ,SAAQ,IAAI;AACzB,SAAK,OAAO,QAAQ;AAAA;AAAA;AAGxB,QAAQ,OAAO;AAEf,sBAAsB,gBAAgB;AAAA,EACpC,YAAY,KAAK;AACf;AACA,SAAK,MAAM;AAAA;AAAA;AAGf,QAAQ,UAAU;AAElB,mBAAmB,gBAAgB;AAAA,EACjC,YAAY,kBAAkB,OAAO;AACnC;AACA,QAAI,EAAE,SAAS,mBAAmB;AAChC,WAAK,aAAa;AAAA,WACb;AACL,WAAK,OAAO;AACZ,UAAI,OAAO;AACT,aAAK,QAAQ;AAAA;AAAA;AAAA;AAAA;AAKrB,QAAQ,OAAO;AAEf,oBAAoB,gBAAgB;AAAA,EAClC,YAAY,WAAW,YAAY,OAAO;AACxC;AACA,QAAI,UAAU,QAAW;AACvB,cAAQ;AACR,mBAAa;AAAA;AAGf,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,QAAQ;AAAA;AAAA;AAGjB,QAAQ,QAAQ;AAWhB,sBAAsB,KAAK;AACzB,SAAO,OAAO,sBAAsB,KAAK,OAAO,OAAK,aAAa,IAAI;AAAA;AAExE,QAAQ,eAAe;AASvB,wBAAwB,KAAK;AAC3B,SAAO,aAAa,KAAK,OAAO,OAAO,KAAK;AAAA;AAE9C,QAAQ,iBAAiB;AASzB,wBAAwB,KAAK;AAC3B,SAAO,MAAM,QAAQ,OAAO,IAAI,SAAS,eAAe,KAAK;AAAA;AAE/D,QAAQ,iBAAiB;AASzB,sBAAsB,KAAK;AACzB,SAAO,CAAC,CAAC,OAAO,EAAE,QAAQ,QAAQ,aAAa,KAAK,WAAW;AAAA;AAEjE,QAAQ,eAAe;AAUvB,0BAA0B,WAAW,YAAY;AAC/C,SAAO,QAAQ,aAAa;AAAA;AAE9B,QAAQ,mBAAmB;AAS3B,4BAA4B,KAAK;AAC/B,QAAM,SAAS,IAAI;AACnB,SAAO,KAAK,KAAK,QAAQ,SAAO;AAC9B,WAAO,SAAS,QAAQ,IAAI;AAAA;AAE9B,SAAO;AAAA;AAET,QAAQ,qBAAqB;AAe7B,kBAAkB,WAAW,SAAS;AACpC,WAAS,OAAO;AAEhB,UAAQ,QAAQ,YAAU;AACxB,QAAI,QAAQ;AACV,eAAS,OAAO;AAEhB,qBAAe,QAAQ,QAAQ,SAAO;AACpC,cAAM,QAAQ,OAAO;AACrB,YACE,UAAU,UACR,EAAE,GAAG,OAAO,OAAO,UAAU,SAC7B,CAAC,OAAO,UAAU,eAAe,KAAK,QAAQ,MAEhD;AACA,iBAAO,OAAO,OAAO;AAAA;AAAA;AAAA;AAAA;AAM7B,SAAO;AAAA;AAET,QAAQ,WAAW;AAYnB,mBAAmB,OAAO,WAAW;AACnC,MAAI,UAAU;AAAW,gBAAY,UAAU;AAE/C,MAAI,CAAC,OAAO,UAAU,eAAe,KAAK,OAAO,SAAS;AACxD,UAAM,SAAS,MAAM,OAAO,IAC1B,WAAS,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ,MAAM;AAEnE,UAAM,OAAO,WAAW,GAAG,aAAa,OAAO,KAAK;AAAA;AAGtD,SAAO;AAAA;AAET,QAAQ,YAAY;AASpB,oBAAoB,MAAM,MAAM;AAC9B,SAAO,KAAK,KAAK,OAAK,KAAK,SAAS;AAAA;AAEtC,QAAQ,aAAa;AAUrB,2BAA2B,OAAkC;AAC3D,SAAO,KAAK,UAAU,OAAO,CAAC,KAAK,WAAU;AAC3C,QAAI,OAAO,WAAU,UAAU;AAC7B,aAAO,OAAO;AAAA;AAGhB,WAAO;AAAA;AAAA;AAIX,QAAQ,oBAAoB;", "names": []}