{"version": 3, "sources": ["../../src/errors/validation-error.ts"], "sourcesContent": ["import type { Model } from '..';\nimport type { ErrorOptions } from './base-error';\nimport BaseError from './base-error';\n\n/**\n * An enum that is used internally by the `ValidationErrorItem` class\n * that maps current `type` strings (as given to ValidationErrorItem.constructor()) to\n * our new `origin` values.\n */\nexport enum ValidationErrorItemType {\n  'notnull violation' = 'CORE',\n  'string violation' = 'CORE',\n  'unique violation' = 'DB',\n  'validation error' = 'FUNCTION',\n}\n\n/**\n * An enum that defines valid ValidationErrorItem `origin` values\n */\nexport enum ValidationErrorItemOrigin {\n  /**\n   * specifies errors that originate from the sequelize \"core\"\n   */\n  CORE = 'CORE',\n\n  /**\n   * specifies validation errors that originate from the storage engine\n   */\n  DB = 'DB',\n\n  /**\n   * specifies validation errors that originate from validator functions (both built-in and custom) defined for a given attribute\n   */\n  FUNCTION = 'FUNCTION',\n}\n\n/**\n * Validation Error Item\n * Instances of this class are included in the `ValidationError.errors` property.\n */\nexport class ValidationErrorItem {\n  /**\n   * @deprecated Will be removed in v7\n   */\n  static TypeStringMap = ValidationErrorItemType;\n\n  /**\n   * @deprecated Will be removed in v7\n   */\n  static Origins = ValidationErrorItemOrigin;\n\n  /**\n   * An error message\n   */\n  readonly message: string;\n\n  /**\n   * The type/origin of the validation error\n   */\n  readonly type: keyof typeof ValidationErrorItemType | null;\n\n  /**\n   * The field that triggered the validation error\n   */\n  readonly path: string | null;\n\n  /**\n   * The value that generated the error\n   */\n  readonly value: string | null;\n\n  readonly origin: keyof typeof ValidationErrorItemOrigin | null;\n\n  /**\n   * The DAO instance that caused the validation error\n   */\n  readonly instance: Model | null;\n\n  /**\n   * A validation \"key\", used for identification\n   */\n  readonly validatorKey: string | null;\n\n  /**\n   * Property name of the BUILT-IN validator function that caused the validation error (e.g. \"in\" or \"len\"), if applicable\n   */\n  readonly validatorName: string | null;\n\n  /**\n   * Parameters used with the BUILT-IN validator function, if applicable\n   */\n  readonly validatorArgs: unknown[];\n\n  /**\n   * Creates a new ValidationError item. Instances of this class are included in the `ValidationError.errors` property.\n   *\n   * @param message An error message\n   * @param type The type/origin of the validation error\n   * @param path The field that triggered the validation error\n   * @param value The value that generated the error\n   * @param instance the DAO instance that caused the validation error\n   * @param validatorKey a validation \"key\", used for identification\n   * @param fnName property name of the BUILT-IN validator function that caused the validation error (e.g. \"in\" or \"len\"), if applicable\n   * @param fnArgs parameters used with the BUILT-IN validator function, if applicable\n   */\n  constructor(\n    message: string,\n    type:\n      | keyof typeof ValidationErrorItemType\n      | keyof typeof ValidationErrorItemOrigin,\n    path: string,\n    value: string,\n    instance: Model,\n    validatorKey: string,\n    fnName: string,\n    fnArgs: unknown[]\n  ) {\n    this.message = message || '';\n    this.type = null;\n    this.path = path || null;\n\n    this.value = value !== undefined ? value : null;\n\n    this.origin = null;\n\n    this.instance = instance || null;\n\n    this.validatorKey = validatorKey || null;\n\n    this.validatorName = fnName || null;\n\n    this.validatorArgs = fnArgs || [];\n\n    if (type) {\n      if (this.isValidationErrorItemOrigin(type)) {\n        this.origin = type;\n      } else {\n        const lowercaseType = this.normalizeString(type);\n        const realType = ValidationErrorItemType[lowercaseType];\n\n        if (realType && ValidationErrorItemOrigin[realType]) {\n          this.origin = realType;\n          this.type = type;\n        }\n      }\n    }\n\n    // This doesn't need captureStackTrace because it's not a subclass of Error\n  }\n\n  private isValidationErrorItemOrigin(\n    origin:\n      | keyof typeof ValidationErrorItemOrigin\n      | keyof typeof ValidationErrorItemType\n  ): origin is keyof typeof ValidationErrorItemOrigin {\n    return (\n      ValidationErrorItemOrigin[\n        origin as keyof typeof ValidationErrorItemOrigin\n      ] !== undefined\n    );\n  }\n\n  private normalizeString<T extends string>(str: T): T {\n    return str.toLowerCase().trim() as T;\n  }\n\n  /**\n   * return a lowercase, trimmed string \"key\" that identifies the validator.\n   *\n   * Note: the string will be empty if the instance has neither a valid `validatorKey` property nor a valid `validatorName` property\n   *\n   * @param useTypeAsNS controls whether the returned value is \"namespace\",\n   *                    this parameter is ignored if the validator's `type` is not one of ValidationErrorItem.Origins\n   * @param NSSeparator a separator string for concatenating the namespace, must be not be empty,\n   *                    defaults to \".\" (fullstop). only used and validated if useTypeAsNS is TRUE.\n   * @throws {Error}    thrown if NSSeparator is found to be invalid.\n   */\n  getValidatorKey(useTypeAsNS: boolean, NSSeparator: string): string {\n    const useTANS = useTypeAsNS === undefined || !!useTypeAsNS;\n    const NSSep = NSSeparator === undefined ? '.' : NSSeparator;\n\n    const type = this.origin;\n    const key = this.validatorKey || this.validatorName;\n    const useNS = useTANS && type && ValidationErrorItemOrigin[type];\n\n    if (useNS && (typeof NSSep !== 'string' || !NSSep.length)) {\n      throw new Error('Invalid namespace separator given, must be a non-empty string');\n    }\n\n    if (!(typeof key === 'string' && key.length)) {\n      return '';\n    }\n\n    return (useNS ? [this.origin, key].join(NSSep) : key).toLowerCase().trim();\n  }\n}\n\n/**\n * Validation Error. Thrown when the sequelize validation has failed. The error contains an `errors` property,\n * which is an array with 1 or more ValidationErrorItems, one for each validation that failed.\n *\n * @param message Error message\n * @param errors Array of ValidationErrorItem objects describing the validation errors\n */\nclass ValidationError extends BaseError {\n  /** Array of ValidationErrorItem objects describing the validation errors */\n  readonly errors: ValidationErrorItem[];\n\n  constructor(\n    message: string,\n    errors: ValidationErrorItem[],\n    options: ErrorOptions = {}\n  ) {\n    super(message);\n    this.name = 'SequelizeValidationError';\n    this.message = 'Validation Error';\n    this.errors = errors || [];\n\n    // Use provided error message if available...\n    if (message) {\n      this.message = message;\n\n      // ... otherwise create a concatenated message out of existing errors.\n    } else if (this.errors.length > 0 && this.errors[0].message) {\n      this.message = this.errors\n        .map(\n          (err: ValidationErrorItem) =>\n            `${err.type || err.origin}: ${err.message}`\n        )\n        .join(',\\n');\n    }\n\n    // Allow overriding the stack if the original stacktrace is uninformative\n    if (options.stack) {\n      this.stack = options.stack;\n    }\n  }\n\n  /**\n   * Gets all validation error items for the path / field specified.\n   *\n   * @param {string} path The path to be checked for error items\n   *\n   * @returns {Array<ValidationErrorItem>} Validation error items for the specified path\n   */\n  get(path: string): ValidationErrorItem[] {\n    return this.errors.reduce<ValidationErrorItem[]>((reduced, error) => {\n      if (error.path === path) {\n        reduced.push(error);\n      }\n      return reduced;\n    }, []);\n  }\n}\n\nexport default ValidationError;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,wBAAsB;AAOf,IAAK,0BAAL,kBAAK,6BAAL;AACL,kDAAsB;AACtB,iDAAqB;AACrB,iDAAqB;AACrB,iDAAqB;AAJX;AAAA;AAUL,IAAK,4BAAL,kBAAK,+BAAL;AAIL,uCAAO;AAKP,qCAAK;AAKL,2CAAW;AAdD;AAAA;AAqBL,0BAA0B;AAAA,EAiE/B,YACE,SACA,MAGA,MACA,OACA,UACA,cACA,QACA,QACA;AA9DO;AAKA;AAKA;AAKA;AAEA;AAKA;AAKA;AAKA;AAKA;AA0BP,SAAK,UAAU,WAAW;AAC1B,SAAK,OAAO;AACZ,SAAK,OAAO,QAAQ;AAEpB,SAAK,QAAQ,UAAU,SAAY,QAAQ;AAE3C,SAAK,SAAS;AAEd,SAAK,WAAW,YAAY;AAE5B,SAAK,eAAe,gBAAgB;AAEpC,SAAK,gBAAgB,UAAU;AAE/B,SAAK,gBAAgB,UAAU;AAE/B,QAAI,MAAM;AACR,UAAI,KAAK,4BAA4B,OAAO;AAC1C,aAAK,SAAS;AAAA,aACT;AACL,cAAM,gBAAgB,KAAK,gBAAgB;AAC3C,cAAM,WAAW,wBAAwB;AAEzC,YAAI,YAAY,0BAA0B,WAAW;AACnD,eAAK,SAAS;AACd,eAAK,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAQZ,4BACN,QAGkD;AAClD,WACE,0BACE,YACI;AAAA;AAAA,EAIF,gBAAkC,KAAW;AACnD,WAAO,IAAI,cAAc;AAAA;AAAA,EAc3B,gBAAgB,aAAsB,aAA6B;AACjE,UAAM,UAAU,gBAAgB,UAAa,CAAC,CAAC;AAC/C,UAAM,QAAQ,gBAAgB,SAAY,MAAM;AAEhD,UAAM,OAAO,KAAK;AAClB,UAAM,MAAM,KAAK,gBAAgB,KAAK;AACtC,UAAM,QAAQ,WAAW,QAAQ,0BAA0B;AAE3D,QAAI,SAAU,QAAO,UAAU,YAAY,CAAC,MAAM,SAAS;AACzD,YAAM,IAAI,MAAM;AAAA;AAGlB,QAAI,CAAE,QAAO,QAAQ,YAAY,IAAI,SAAS;AAC5C,aAAO;AAAA;AAGT,WAAQ,SAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,SAAS,KAAK,cAAc;AAAA;AAAA;AArJ/D,cAJF,qBAIE,iBAAgB;AAKhB,cATF,qBASE,WAAU;AA2JnB,8BAA8B,0BAAU;AAAA,EAItC,YACE,SACA,QACA,UAAwB,IACxB;AACA,UAAM;AAPC;AAQP,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,SAAS,UAAU;AAGxB,QAAI,SAAS;AACX,WAAK,UAAU;AAAA,eAGN,KAAK,OAAO,SAAS,KAAK,KAAK,OAAO,GAAG,SAAS;AAC3D,WAAK,UAAU,KAAK,OACjB,IACC,CAAC,QACC,GAAG,IAAI,QAAQ,IAAI,WAAW,IAAI,WAErC,KAAK;AAAA;AAIV,QAAI,QAAQ,OAAO;AACjB,WAAK,QAAQ,QAAQ;AAAA;AAAA;AAAA,EAWzB,IAAI,MAAqC;AACvC,WAAO,KAAK,OAAO,OAA8B,CAAC,SAAS,UAAU;AACnE,UAAI,MAAM,SAAS,MAAM;AACvB,gBAAQ,KAAK;AAAA;AAEf,aAAO;AAAA,OACN;AAAA;AAAA;AAIP,IAAO,2BAAQ;", "names": []}