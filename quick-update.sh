#!/bin/bash

# Quick Layer Update Script - Makes versioning seamless
set -e

LAYER_NAME="my-node-app-layer"
FUNCTION_NAME="my-node-app-api"
REGION="us-east-1"  # Change to your region

echo "🚀 Quick Layer Update (Creating new version automatically)..."

# Build and deploy in one command
cd lambda-layer
npm install --silent
zip -r -q ../my-node-app-layer.zip .
cd ..

echo "📦 Uploading updated layer..."
NEW_VERSION=$(aws lambda publish-layer-version \
  --layer-name $LAYER_NAME \
  --zip-file fileb://my-node-app-layer.zip \
  --compatible-runtimes nodejs18.x nodejs20.x \
  --region $REGION \
  --query 'Version' \
  --output text)

echo "🔄 Updating function to use version $NEW_VERSION..."
aws lambda update-function-configuration \
  --function-name $FUNCTION_NAME \
  --layers "arn:aws:lambda:$REGION:$(aws sts get-caller-identity --query Account --output text):layer:$LAYER_NAME:$NEW_VERSION" \
  --region $REGION \
  --query 'FunctionName' \
  --output text > /dev/null

echo "✅ Update complete! Function now uses layer version $NEW_VERSION"

# Clean up old versions (keep last 3)
echo "🧹 Cleaning up old versions (keeping last 3)..."
aws lambda list-layer-versions \
  --layer-name $LAYER_NAME \
  --region $REGION \
  --query 'LayerVersions[3:].Version' \
  --output text | tr '\t' '\n' | while read version; do
    if [ ! -z "$version" ]; then
      echo "  Deleting version $version..."
      aws lambda delete-layer-version \
        --layer-name $LAYER_NAME \
        --version-number $version \
        --region $REGION 2>/dev/null || true
    fi
done

echo "🎉 All done! Your changes are live."
