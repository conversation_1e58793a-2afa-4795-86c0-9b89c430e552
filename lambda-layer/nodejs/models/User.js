const { DataTypes } = require('sequelize');
const { sequelize } = require('../db');

const Users = sequelize.define('Users', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  status: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  uuid: {
    type: DataTypes.UUID,
    allowNull: true,
    defaultValue: DataTypes.UUIDV4
  },
  employee_id: {
    type: DataTypes.STRING,
    allowNull: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      isEmail: true
    }
  },
  countryCode: {
    type: DataTypes.STRING,
    allowNull: true
  },
  official_number: {
    type: DataTypes.STRING,
    allowNull: true
  },
  firstName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  lastName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  attachmentId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  profileImageUrl: {
    type: DataTypes.STRING,
    allowNull: true
  },
  roleId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  virtual_number: {
    type: DataTypes.STRING,
    allowNull: true
  },
  can_approve: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  teamId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  isAproved: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  managerId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  cityId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  region: {
    type: DataTypes.STRING,
    allowNull: true
  },
  departmentId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  view_email: {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    defaultValue: false
  },
  email_credit: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  view_phone: {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    defaultValue: false
  },
  phone_credit: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  current_bussiness_unit_id: {
    type: DataTypes.INTEGER,
    allowNull: true
  }
}, {
  tableName: 'user', // This matches your existing table name
  timestamps: true, // This will use the existing created_at and updated_at fields
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = Users;
