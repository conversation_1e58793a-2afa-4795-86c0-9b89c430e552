const { Sequelize } = require('sequelize');

// connect to postgres
const sequelize = new Sequelize('miles_force_7', 'postgres', '_uGO*fTIEN752fs?', {
  host: '************',
  dialect: 'postgres',
  logging: false, // disable SQL logs
});

async function connectDB() {
  try {
    await sequelize.authenticate();
    console.log('✅ Database connected successfully');
  } catch (error) {
    console.error('❌ Unable to connect to DB:', error);
  }
}

connectDB();

module.exports = sequelize;
