const { Sequelize } = require('sequelize');

// Database configuration with environment variables for flexibility
const sequelize = new Sequelize(
  process.env.DB_NAME || 'miles_force_7',
  process.env.DB_USER || 'postgres',
  process.env.DB_PASSWORD || '_uGO*fTIEN752fs?',
  {
    host: process.env.DB_HOST || '************',
    port: process.env.DB_PORT || 5432,
    dialect: 'postgres',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 2, // Reduced for Lambda
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
);

// Connection management - no auto-connect, let Lambda function control this
let isConnected = false;

async function connectDB() {
  if (!isConnected) {
    try {
      await sequelize.authenticate();
      console.log('✅ Database connected successfully');
      isConnected = true;
    } catch (error) {
      console.error('❌ Unable to connect to DB:', error);
      throw error;
    }
  }
  return sequelize;
}

// Export both sequelize instance and connection function
module.exports = { sequelize, connectDB };
