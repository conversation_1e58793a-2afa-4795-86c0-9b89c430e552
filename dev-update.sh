#!/bin/bash

# Development Layer Update - Uses date-based naming
set -e

LAYER_NAME="my-node-app-layer-dev"
FUNCTION_NAME="my-node-app-api-dev"
REGION="us-east-1"

# Delete existing dev layer if it exists
echo "🗑️  Cleaning up existing dev layer..."
aws lambda list-layer-versions \
  --layer-name $LAYER_NAME \
  --region $REGION \
  --query 'LayerVersions[].Version' \
  --output text | tr '\t' '\n' | while read version; do
    if [ ! -z "$version" ]; then
      aws lambda delete-layer-version \
        --layer-name $LAYER_NAME \
        --version-number $version \
        --region $REGION 2>/dev/null || true
    fi
done

# Create fresh layer
echo "📦 Creating fresh dev layer..."
cd lambda-layer
npm install --silent
zip -r -q ../my-node-app-layer.zip .
cd ..

NEW_VERSION=$(aws lambda publish-layer-version \
  --layer-name $LAYER_NAME \
  --zip-file fileb://my-node-app-layer.zip \
  --compatible-runtimes nodejs18.x nodejs20.x \
  --region $REGION \
  --query 'Version' \
  --output text)

echo "🔄 Updating dev function..."
aws lambda update-function-configuration \
  --function-name $FUNCTION_NAME \
  --layers "arn:aws:lambda:$REGION:$(aws sts get-caller-identity --query Account --output text):layer:$LAYER_NAME:$NEW_VERSION" \
  --region $REGION > /dev/null

echo "✅ Dev environment updated! Layer version: $NEW_VERSION"
