AWSTemplateFormatVersion: "2010-09-09"
Description: "My Node App Lambda Function with Layer"

Parameters:
  LayerS3Bucket:
    Type: String
    Description: S3 bucket containing the layer zip file
  LayerS3Key:
    Type: String
    Description: S3 key for the layer zip file
    Default: my-node-app-layer.zip
  FunctionS3Bucket:
    Type: String
    Description: S3 bucket containing the function zip file
  FunctionS3Key:
    Type: String
    Description: S3 key for the function zip file
    Default: my-node-app-function.zip

Resources:
  # Lambda Execution Role
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        - arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole
      Policies:
        - PolicyName: DatabaseAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - rds:DescribeDBInstances
                  - rds:DescribeDBClusters
                Resource: "*"

  # Lambda Layer
  MyNodeAppLayer:
    Type: AWS::Lambda::LayerVersion
    Properties:
      LayerName: my-node-app-layer
      Description: !Sub "Common dependencies for my-node-app - Updated ${AWS::StackName}"
      Content:
        S3Bucket: !Ref LayerS3Bucket
        S3Key: !Ref LayerS3Key
      CompatibleRuntimes:
        - nodejs18.x
        - nodejs20.x

  # Lambda Function
  MyNodeAppFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: my-node-app-api
      Runtime: nodejs20.x
      Handler: index.handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Code:
        S3Bucket: !Ref FunctionS3Bucket
        S3Key: !Ref FunctionS3Key
      Layers:
        - !Ref MyNodeAppLayer
      Timeout: 30
      MemorySize: 512
      Environment:
        Variables:
          NODE_ENV: production

  # API Gateway
  ApiGateway:
    Type: AWS::ApiGateway::RestApi
    Properties:
      Name: my-node-app-api
      Description: API for my-node-app
      EndpointConfiguration:
        Types:
          - REGIONAL

  # API Gateway Resource
  UsersResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: users

  # API Gateway Methods
  UsersGetMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref UsersResource
      HttpMethod: GET
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${MyNodeAppFunction.Arn}/invocations"

  UsersPostMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref UsersResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${MyNodeAppFunction.Arn}/invocations"

  # Lambda Permission for API Gateway
  LambdaApiGatewayPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref MyNodeAppFunction
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "${ApiGateway}/*/*"

  # API Gateway Deployment
  ApiGatewayDeployment:
    Type: AWS::ApiGateway::Deployment
    DependsOn:
      - UsersGetMethod
      - UsersPostMethod
    Properties:
      RestApiId: !Ref ApiGateway
      StageName: prod

Outputs:
  ApiUrl:
    Description: API Gateway URL
    Value: !Sub "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/prod"
  LambdaFunctionArn:
    Description: Lambda Function ARN
    Value: !GetAtt MyNodeAppFunction.Arn
  LayerArn:
    Description: Lambda Layer ARN
    Value: !Ref MyNodeAppLayer
