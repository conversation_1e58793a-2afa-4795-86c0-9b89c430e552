const http = require('http');
const url = require('url');
const { Client } = require('pg');

// const client = new Client({
//   user: 'postgres',
//   host: '************',
//   database: 'miles_force_7',
//   password: '_uGO*fTIEN752fs?',
//   port: 5432,
// });

async function startServer() {
  // await client.connect();
  console.log("✅ Connected to PostgreSQL");

  const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);

    if (parsedUrl.pathname === '/users' && req.method === 'GET') {
        try{
        console.log("GET /users");
      const result = await client.query('SELECT * FROM "user"');
      console.log(result.rows);
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(result.rows));
        }catch(e){
            console.log(e);
        }
    } 
    
    else if (parsedUrl.pathname === '/users' && req.method === 'POST') {
      let body = '';
      req.on('data', chunk => { body += chunk; });
      req.on('end', async () => {
        const user = JSON.parse(body);
        await client.query('INSERT INTO users(name, email) VALUES($1, $2)', [user.name, user.email]);
        res.writeHead(201, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ message: "User added", user }));
      });
    } 
    
    else {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('404 Not Found');
    }
  });

  server.listen(3000, () => {
    console.log("Server running at http://localhost:3000");
  });
}

startServer().catch(err => console.error(err));