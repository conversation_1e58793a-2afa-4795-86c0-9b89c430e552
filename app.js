const http = require('http');
const url = require('url');
const sequelize = require('./db');
const User = require('./models/User');

async function startServer() {
  try {
    await sequelize.authenticate();
    console.log("✅ Connected to PostgreSQL via Sequelize");

    // Sync the models with the database (optional - creates tables if they don't exist)
    await sequelize.sync();
  } catch (error) {
    console.error("❌ Unable to connect to database:", error);
    return;
  }

  const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);

    if (parsedUrl.pathname === '/users' && req.method === 'GET') {
        try {
          console.log("GET /users");
          const users = await User.findAll();
          console.log(users);
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify(users));
        } catch (e) {
          console.log(e);
          res.writeHead(500, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: 'Internal server error' }));
        }
    }
    
    else if (parsedUrl.pathname === '/users' && req.method === 'POST') {
      let body = '';
      req.on('data', chunk => { body += chunk; });
      req.on('end', async () => {
        try {
          const userData = JSON.parse(body);
          const user = await User.create({
            name: userData.name,
            email: userData.email
          });
          res.writeHead(201, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ message: "User added", user }));
        } catch (error) {
          console.error('Error creating user:', error);
          res.writeHead(400, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: 'Failed to create user', details: error.message }));
        }
      });
    }
    
    else {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('404 Not Found');
    }
  });

  server.listen(3000, () => {
    console.log("Server running at http://localhost:3000");
  });
}

startServer().catch(err => console.error(err));