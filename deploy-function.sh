#!/bin/bash

# <PERSON><PERSON>t to deploy Lambda Function
echo "🚀 Deploying Lambda Function..."

# Navigate to function directory
cd lambda-function

# Create deployment package
echo "📦 Creating function package..."
zip -r ../my-node-app-function.zip .

# Go back to root directory
cd ..

echo "✅ Function package created: my-node-app-function.zip"
echo ""
echo "Next steps:"
echo "1. Upload my-node-app-function.zip to AWS Lambda"
echo "2. Add the Layer ARN to your Lambda function configuration"
echo "3. Set up API Gateway to trigger the Lambda function"
echo ""
echo "AWS CLI command to create function (replace LAYER_ARN and ROLE_ARN):"
echo "aws lambda create-function \\"
echo "  --function-name my-node-app-api \\"
echo "  --runtime nodejs20.x \\"
echo "  --role arn:aws:iam::YOUR_ACCOUNT:role/YOUR_LAMBDA_ROLE \\"
echo "  --handler index.handler \\"
echo "  --zip-file fileb://my-node-app-function.zip \\"
echo "  --timeout 30 \\"
echo "  --memory-size 512 \\"
echo "  --layers arn:aws:lambda:YOUR_REGION:YOUR_ACCOUNT:layer:my-node-app-layer:1"
