#!/bin/bash

# <PERSON><PERSON>t to deploy Lambda Layer
echo "🚀 Deploying Lambda Layer..."

# Navigate to layer directory
cd lambda-layer

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Create deployment package
echo "📦 Creating layer package..."
zip -r ../my-node-app-layer.zip .

# Go back to root directory
cd ..

echo "✅ Layer package created: my-node-app-layer.zip"
echo ""
echo "Next steps:"
echo "1. Upload my-node-app-layer.zip to AWS Lambda Layers"
echo "2. Note the Layer ARN for use in your Lambda function"
echo ""
echo "AWS CLI command to create layer:"
echo "aws lambda publish-layer-version \\"
echo "  --layer-name my-node-app-layer \\"
echo "  --description 'Common dependencies for my-node-app' \\"
echo "  --zip-file fileb://my-node-app-layer.zip \\"
echo "  --compatible-runtimes nodejs18.x nodejs20.x"
