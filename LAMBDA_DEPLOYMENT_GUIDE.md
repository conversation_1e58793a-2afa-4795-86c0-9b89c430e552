# Lambda Deployment Guide

This guide will help you deploy your Node.js application as an AWS Lambda function with a Lambda Layer for common dependencies.

## Project Structure

```
├── lambda-layer/
│   └── nodejs/
│       ├── package.json          # Layer dependencies
│       ├── db.js                 # Database configuration
│       └── models/
│           └── User.js           # User model
├── lambda-function/
│   ├── index.js                  # Lambda handler
│   └── package.json              # Function metadata
├── deploy-layer.sh               # Layer deployment script
├── deploy-function.sh            # Function deployment script
├── cloudformation-template.yaml  # CloudFormation template
└── LAMBDA_DEPLOYMENT_GUIDE.md    # This guide
```

## Prerequisites

1. AWS CLI installed and configured
2. Appropriate IAM permissions for Lambda, API Gateway, and CloudFormation
3. Node.js installed locally

## Deployment Options

### Option 1: Manual Deployment (Recommended for learning)

#### Step 1: Deploy the Lambda Layer

```bash
# Make the script executable
chmod +x deploy-layer.sh

# Run the deployment script
./deploy-layer.sh
```

This will create `my-node-app-layer.zip`. Upload it to AWS Lambda Layers:

```bash
aws lambda publish-layer-version \
  --layer-name my-node-app-layer \
  --description 'Common dependencies for my-node-app' \
  --zip-file fileb://my-node-app-layer.zip \
  --compatible-runtimes nodejs18.x nodejs20.x
```

Note the Layer ARN from the response.

#### Step 2: Deploy the Lambda Function

```bash
# Make the script executable
chmod +x deploy-function.sh

# Run the deployment script
./deploy-function.sh
```

Create the Lambda function (replace placeholders):

```bash
aws lambda create-function \
  --function-name my-node-app-api \
  --runtime nodejs20.x \
  --role arn:aws:iam::YOUR_ACCOUNT:role/YOUR_LAMBDA_ROLE \
  --handler index.handler \
  --zip-file fileb://my-node-app-function.zip \
  --timeout 30 \
  --memory-size 512 \
  --layers arn:aws:lambda:YOUR_REGION:YOUR_ACCOUNT:layer:my-node-app-layer:1
```

#### Step 3: Set up API Gateway

1. Create a new REST API in API Gateway
2. Create a `/users` resource
3. Add GET and POST methods
4. Configure Lambda proxy integration
5. Deploy the API

### Option 2: CloudFormation Deployment (Recommended for production)

#### Step 1: Upload packages to S3

```bash
# Create S3 bucket (if needed)
aws s3 mb s3://your-deployment-bucket

# Upload layer package
aws s3 cp my-node-app-layer.zip s3://your-deployment-bucket/

# Upload function package
aws s3 cp my-node-app-function.zip s3://your-deployment-bucket/
```

#### Step 2: Deploy CloudFormation stack

```bash
aws cloudformation create-stack \
  --stack-name my-node-app-stack \
  --template-body file://cloudformation-template.yaml \
  --parameters ParameterKey=LayerS3Bucket,ParameterValue=your-deployment-bucket \
               ParameterKey=FunctionS3Bucket,ParameterValue=your-deployment-bucket \
  --capabilities CAPABILITY_IAM
```

## Environment Variables

Consider adding these environment variables to your Lambda function:

- `NODE_ENV`: Set to 'production'
- `DB_HOST`: Database host (if different from hardcoded)
- `DB_NAME`: Database name (if different from hardcoded)
- `DB_USER`: Database user (if different from hardcoded)
- `DB_PASSWORD`: Database password (consider using AWS Secrets Manager)

## Security Considerations

1. **Database Credentials**: Move database credentials to AWS Secrets Manager
2. **VPC Configuration**: If your database is in a VPC, configure Lambda VPC settings
3. **IAM Roles**: Use least-privilege IAM roles
4. **API Gateway**: Consider adding authentication (API Keys, Cognito, etc.)

## Testing

Test your deployed Lambda function:

```bash
# Test via AWS CLI
aws lambda invoke \
  --function-name my-node-app-api \
  --payload '{"httpMethod":"GET","path":"/users"}' \
  response.json

# Check the response
cat response.json
```

## Monitoring

- Enable CloudWatch logs for your Lambda function
- Set up CloudWatch alarms for errors and duration
- Consider using AWS X-Ray for distributed tracing

## Cost Optimization

- Adjust memory allocation based on actual usage
- Consider using Provisioned Concurrency for consistent performance
- Monitor and optimize cold start times

## Troubleshooting

Common issues:
1. **Database connection timeouts**: Increase Lambda timeout, check VPC configuration
2. **Layer not found**: Verify layer ARN and region
3. **Permission errors**: Check IAM roles and policies
4. **Cold starts**: Consider connection pooling and keep-alive strategies
