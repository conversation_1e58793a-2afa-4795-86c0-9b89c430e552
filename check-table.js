const { Client } = require('pg');

const client = new Client({
  user: 'postgres',
  host: '************',
  database: 'miles_force_7',
  password: '_uGO*fTIEN752fs?',
  port: 5432,
});

async function checkTable() {
  try {
    await client.connect();
    console.log("✅ Connected to PostgreSQL");
    
    // Check if table exists and get its structure
    const tableInfo = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'user'
      ORDER BY ordinal_position;
    `);
    
    console.log("Current 'user' table structure:");
    console.table(tableInfo.rows);
    
    // Also check what data exists
    const userData = await client.query('SELECT * FROM "user" LIMIT 5');
    console.log("\nSample data from 'user' table:");
    console.table(userData.rows);
    
  } catch (error) {
    console.error("Error:", error.message);
  } finally {
    await client.end();
  }
}

checkTable();
