const { sequelize, connectDB } = require('/opt/nodejs/db');
const Users = require('/opt/nodejs/models/User');

async function connectToDatabase() {
  try {
    await connectDB(); // Use the centralized connection function
    await sequelize.sync(); // Sync models with existing database structure
  } catch (error) {
    console.error('❌ Unable to connect to database:', error);
    throw error;
  }
}

// Helper function to create API Gateway response
function createResponse(statusCode, body, headers = {}) {
  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      ...headers
    },
    body: JSON.stringify(body)
  };
}

// GET /users handler
async function getUsers() {
  try {
    console.log("GET /users");
    const users = await Users.findAll();
    console.log(`Found ${users.length} users`);
    return createResponse(200, users);
  } catch (error) {
    console.error('Error fetching users:', error);
    return createResponse(500, { error: 'Internal server error', details: error.message });
  }
}

// POST /users handler
async function createUser(body) {
  try {
    const userData = JSON.parse(body);
    const user = await Users.create({
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: userData.email,
      employee_id: userData.employee_id || null,
      countryCode: userData.countryCode || null,
      official_number: userData.official_number || null
    });
    console.log('User created:', user.id);
    return createResponse(201, { message: "User added", user });
  } catch (error) {
    console.error('Error creating user:', error);
    return createResponse(400, { error: 'Failed to create user', details: error.message });
  }
}

// Main Lambda handler
exports.handler = async (event) => {
  console.log('Event:', JSON.stringify(event, null, 2));
  
  try {
    // Connect to database
    await connectToDatabase();
    
    const { httpMethod, path, body } = event;
    
    // Handle CORS preflight requests
    if (httpMethod === 'OPTIONS') {
      return createResponse(200, { message: 'CORS preflight' });
    }
    
    // Route handling
    if (path === '/users' && httpMethod === 'GET') {
      return await getUsers();
    } else if (path === '/users' && httpMethod === 'POST') {
      return await createUser(body);
    } else {
      return createResponse(404, { error: 'Not Found' });
    }
    
  } catch (error) {
    console.error('Lambda execution error:', error);
    return createResponse(500, { error: 'Internal server error', details: error.message });
  }
};
